室外场景程序化建模标签详解 (Low Poly风格 - Unity)
基于末日丧尸游戏故事背景的程序化建模标签系统

【游戏背景概述】
本标签系统专为末日丧尸题材的车辆驾驶生存游戏设计。游戏分为六个主要阶段：
1. 逃离都市炼狱 & 初获座驾 (章节1-28)
2. 末日公路狂飙 & 资源争夺 (章节29-88)
3. 深入险境 & 钢铁洪流的对决 (章节89-136)
4. 破碎世界 & 危殆同盟 (章节137-176)
5. 旧世回响 & 智械危机 (章节177-216)
6. 最终地平线 & 人类命运 (章节217-236)

玩家驾驶改装车辆在末世道路上求生、战斗、寻找资源，并探索室内建筑进行搜刮和解谜。

【标签系统说明】
以下标签旨在为程序化生成系统提供足够的信息，以便根据当前游戏阶段、故事主题、破坏程度、丧尸威胁等级等全局参数，智能地选择和放置合适的模块与装饰，营造符合末日氛围的场景环境。
1.0 道路网络 (Road Network)
此分类下的标签主要用于定义道路的物理属性、视觉特征、连接性以及其在末日环境中的状态。

【游戏阶段关联说明】
- 第一阶段(1-28章)：主要涉及城市道路、地下车库、高架桥等都市环境
- 第二阶段(29-88章)：重点为高速公路、乡村道路、荒野小径等开放道路
- 第三阶段(89-136章)：包含地下隧道、军事基地道路、特殊环境道路
- 第四阶段(137-176章)：跨大陆桥梁、异域地形道路、浮空结构
- 第五阶段(177-216章)：AI控制区域道路、高科技设施通道
- 第六阶段(217-236章)：终极设施入口、神秘圣地道路

1.1 类型/层级 (Type/Hierarchy):

【都市逃亡道路 - 第一阶段专用】
•	Road_CityMainStreet_Burning (燃烧的城市主干道 - 主题01)
•	Road_CityMainStreet_VehicleBlocked (车辆堵塞的城市主干道 - 主题01)
•	Road_UndergroundGarage_Spiral (地下车库螺旋车道 - 主题02)
•	Road_UndergroundGarage_Level (地下车库平层车道 - 主题02)
•	Road_Overpass_Elevated_Damaged (受损高架桥路段 - 主题03)
•	Road_Overpass_OnRamp (高架桥匝道 - 主题03)
•	Road_Tunnel_Mountain_Emergency (山体隧道紧急通道 - 主题04)
•	Road_Tunnel_Subway_Abandoned (废弃地铁隧道 - 主题05)
•	Road_Highway_Checkpoint_Abandoned (废弃高速检查站 - 主题06)
•	Road_IndustrialZone_Loading (工业区装卸道路 - 主题07)
•	Road_Residential_Suburban (郊区住宅街道 - 主题08)
•	Road_Commercial_ShoppingDistrict (商业区购物街 - 主题09)
•	Road_Airport_Service (机场服务道路 - 主题10)
•	Road_Waterfront_Dock (滨水区码头道路 - 主题11)
•	Road_Park_Internal (公园内部道路 - 主题12)
•	Road_Mountain_Winding_Narrow (山区盘山公路 - 主题13)
•	Road_Warehouse_Internal (仓库区内部道路 - 主题14)

【末日公路道路 - 第二阶段专用】
•	Road_Highway_Interstate_Abandoned (废弃州际高速公路 - 主题15-44)
•	Road_Desert_Straight_Endless (沙漠直线公路 - 主题15)
•	Road_Coastal_Scenic_Damaged (受损沿海观光路 - 主题16)
•	Road_Forest_Winding_Overgrown (森林蜿蜒道路 - 主题17)
•	Road_Mountain_Pass_Treacherous (山口险路 - 主题18)
•	Road_Farmland_Rural_Dirt (农田乡村土路 - 主题19)
•	Road_GasStation_Access (加油站出入道路 - 主题20)
•	Road_Settlement_Perimeter (定居点外围道路 - 主题21-22)
•	Road_Military_Base_Secured (军事基地安全道路 - 主题23)
•	Road_PowerPlant_Service (发电厂服务道路 - 主题24)
•	Road_Dam_Maintenance (水坝维护道路 - 主题25)
•	Road_City_Fortified_Approach (方舟城接近道路 - 主题26-28)
•	Road_WeaponTest_Restricted (武器试验场限制道路 - 主题29)
•	Road_Volcano_Observatory_Access (火山观测站通道 - 主题30)
•	Road_Research_Campus_Internal (研究园区内部道路 - 主题31)
•	Road_Desert_BuriedCity_Excavated (沙漠古城挖掘道路 - 主题32)
•	Road_Platform_Research_Offshore (离岸研究平台道路 - 主题33)
•	Road_SpaceElevator_Approach (太空电梯接近道路 - 主题34)
•	Road_Factory_Automated_Clean (自动化工厂洁净道路 - 主题35)
•	Road_TreeCanopy_Elevated (树冠层高架道路 - 主题36)
•	Road_FloatingStructure_SciFi (科幻浮空结构道路 - 主题37)
•	Road_Memorial_Ceremonial (纪念碑仪式道路 - 主题38)
•	Road_Bridge_Megastructure_Partial (巨型桥梁部分路段 - 主题39)
•	Road_SaltFlats_Crystalline (结晶盐碱地道路 - 主题40)
•	Road_FungalForest_Spore (真菌森林孢子道路 - 主题41)
•	Road_Coastal_Shipwreck (海岸沉船区道路 - 主题42)
•	Road_Museum_Exhibition_Internal (博物馆展厅内部道路 - 主题43)
•	Road_Ship_Deck_Massive (巨型船只甲板道路 - 主题44)
•	Road_SeedBank_Secure_Underground (地下种子银行安全通道 - 主题44)

【深入险境道路 - 第三阶段专用】
•	Road_Underground_Metro_Flooded (被淹地下地铁 - 主题45)
•	Road_Underground_Highway_Collapsed (坍塌地下高速 - 主题46)
•	Road_Sewer_MainTunnel (下水道主隧道 - 主题47)
•	Road_Cave_Natural_Carved (天然洞穴车道 - 主题48)
•	Road_Bunker_Military_Reinforced (军事掩体加固道路 - 主题49)
•	Road_PowerPlant_Nuclear_Restricted (核电站限制道路 - 主题50)
•	Road_City_Siege_Defensive (围城防御道路 - 主题51-53)
•	Road_Evacuation_Emergency (紧急疏散道路 - 主题54)
•	Road_Horizon_NewFrontier (新地平线道路 - 主题55)

【破碎世界道路 - 第四阶段专用】
•	Road_Bridge_Continental_Ruined (跨大陆桥废墟 - 主题69)
•	Road_Tundra_Frozen_Permafrost (冻原永久冻土路 - 主题70)
•	Road_FungalForest_Mycelium (真菌森林菌丝路 - 主题71)
•	Road_Coastal_Shipgraveyard (海岸沉船墓地路 - 主题72)
•	Road_FloatingCity_Pontoon (水上都市浮桥路 - 主题73)
•	Road_SkyCity_Fallen_Debris (天空之城坠落路 - 主题74)
•	Road_Polar_IceCave_Tunnel (极地冰洞隧道 - 主题75)
•	Road_CrystalMine_Luminous (发光水晶矿道 - 主题76)
•	Road_MobileFortress_Deck (移动要塞甲板路 - 主题77)
•	Road_SonicWeapon_Resonance (音波武器共振路 - 主题78)
•	Road_AlienCrash_Impact (外星坠毁撞击路 - 主题79)
•	Road_MutantWildlife_Territory (变异动物领地路 - 主题80)
•	Road_Underwater_Volcanic (海底火山路 - 主题81)
•	Road_Airship_Storm_Aerial (风暴飞艇空中路 - 主题82)
•	Road_Dimensional_Rift_Unstable (维度裂隙不稳路 - 主题83)
•	Road_MemoryPalace_Shifting (记忆宫殿变化路 - 主题84)
•	Road_Biosphere_Dome_Internal (生物圈穹顶内路 - 主题85)
•	Road_Junkyard_Mega_Compacted (巨型垃圾场压实路 - 主题86)
•	Road_GeothermalDrill_Platform (地热钻井平台路 - 主题87)

【智械危机道路 - 第五阶段专用】
•	Road_AICore_DataCable_Network (AI核心数据线路 - 主题89)
•	Road_NaniteSwarm_Metallic (纳米集群金属路 - 主题90)
•	Road_SpaceElevator_Debris_Field (太空电梯残骸路 - 主题91)
•	Road_BrainVault_Hidden_Access (缸中之脑隐蔽路 - 主题92)
•	Road_BioWeapon_Containment (生物武器隔离路 - 主题93)
•	Road_QuantumComputer_Anomaly (量子计算机异常路 - 主题94)
•	Road_WeatherControl_Storm (天气控制风暴路 - 主题95)
•	Road_CloningFacility_Sterile (克隆工厂无菌路 - 主题96)
•	Road_DigitalCemetery_Server (数字公墓服务器路 - 主题97)
•	Road_SpaceStation_Orbital_Crash (轨道空间站坠毁路 - 主题98)
•	Road_HiveMind_Neural_Network (蜂巢思维神经路 - 主题99)
•	Road_MaglevLine_Abandoned_Track (废弃磁悬浮轨道 - 主题100)
•	Road_BioLab_Secret_Disguised (秘密生化基地伪装路 - 主题101)
•	Road_HolographicCity_Projection (全息投影城市路 - 主题102)
•	Road_Monastery_Isolated_Ancient (与世隔绝修道院路 - 主题103)
•	Road_OilRigCity_Vertical_Maze (石油钻井城市路 - 主题104)
•	Road_WorldTree_Mega_Branch (世界树巨型枝干路 - 主题105)
•	Road_EternalStorm_Eye_Calm (永恒风暴之眼路 - 主题106)
•	Road_LabyrinthCity_Modular (迷宫之城模块路 - 主题107)
•	Road_ProphetTemple_Forbidden (先知圣殿禁地路 - 主题108)

【最终命运道路 - 第六阶段专用】
•	Road_EdenDome_Paradise_Approach (伊甸园穹顶接近路 - 主题109)
•	Road_ArkLaunch_Final_Countdown (方舟发射最终路 - 主题110)
•	Road_OriginLab_Virus_Source (病毒起源实验室路 - 主题111)
•	Road_WorldEngine_Geocentric (世界引擎地心路 - 主题112)
•	Road_Multiverse_Nexus_Reality (多重宇宙交汇路 - 主题113)
•	Road_Ascension_Energy_Sanctum (飞升能量圣域路 - 主题114)
•	Road_Entropy_Void_Universe (寂灭虚无宇宙路 - 主题115)
•	Road_Creator_Workshop_Stellar (造物者星际工坊路 - 主题116)
•	Road_Choice_Nexus_Abstract (抉择之门抽象路 - 主题117)
•	Road_FinalBattle_Destiny (最终决战命运路 - 主题118)

1.2 材质 (Material):

【基础道路材质】
•	Material_Asphalt_New (新沥青 - 方舟城等高级区域)
•	Material_Asphalt_Worn (磨损沥青 - 一般城市道路)
•	Material_Asphalt_Cracked (龟裂沥青 - 废弃城市道路)
•	Material_Asphalt_Melted (熔化沥青 - 火灾/高温区域)
•	Material_Asphalt_Bloodstained (血迹沥青 - 丧尸袭击现场)

【混凝土材质】
•	Material_Concrete_Smooth (光滑混凝土 - 现代建筑)
•	Material_Concrete_Rough (粗糙混凝土 - 工业设施)
•	Material_Concrete_Reinforced (钢筋混凝土 - 军事设施)
•	Material_Concrete_Cracked_Radiation (辐射龟裂混凝土 - 核电站)
•	Material_Concrete_Bloodstained (血迹混凝土 - 战斗现场)
•	Material_Concrete_Scorched (烧焦混凝土 - 爆炸现场)

【自然材质】
•	Material_Gravel (碎石路 - 乡村道路)
•	Material_Gravel_Contaminated (污染碎石 - 化学泄漏区)
•	Material_Dirt_Packed (压实的土路 - 农村地区)
•	Material_Dirt_Loose (松散的土路 - 荒废区域)
•	Material_Dirt_Muddy (泥泞土路 - 雨后/沼泽)
•	Material_Dirt_Radioactive (放射性土壤 - 核污染区)
•	Material_Sand_Desert (沙漠沙土 - 沙漠公路)
•	Material_Sand_Contaminated (污染沙土 - 化学武器试验场)

【特殊末日材质】
•	Material_Cobblestone_Ancient (古代鹅卵石 - 历史遗迹)
•	Material_Mud_Toxic (有毒泥浆 - 化学污染区)
•	Material_SteelPlate_Temporary (临时钢板路面 - 紧急修复)
•	Material_SteelPlate_Rusted (锈蚀钢板 - 废弃工业区)
•	Material_Metal_SteelDeck (金属甲板 - 船只/平台)
•	Material_Metal_Grated (金属格栅 - 工业走道)
•	Material_Metal_Corrugated (波纹金属板 - 临时建筑)

【有机/变异材质】
•	Material_Wood_LivingTree (活体树木 - 巨型树枝路径)
•	Material_Wood_Rotting (腐烂木材 - 废弃木桥)
•	Material_Flesh_Organic (有机血肉 - 病毒感染区)
•	Material_Fungal_Mycelium (真菌菌丝 - 真菌森林)
•	Material_Crystal_Growing (生长水晶 - 水晶矿洞)
•	Material_Crystal_Radioactive (放射性水晶 - 异常区域)

【高科技材质】
•	Material_ScrapMetal_RoadSurface (废金属路面 - 垃圾场)
•	Material_ScrapMetal_Welded (焊接废金属 - 拾荒者建造)
•	Material_Composite_SciFi (科幻复合材料 - 高科技设施)
•	Material_Energy_Solidified (固化能量 - 超自然区域)
•	Material_Nanite_SelfRepairing (自修复纳米材料 - AI控制区)
•	Material_Holographic_Projection (全息投影材料 - 虚拟城市)

【冰雪/极地材质】
•	Material_Ice_Thick (厚冰层 - 冰封河道)
•	Material_Ice_Cracked (裂冰 - 不稳定冰面)
•	Material_Snow_Packed (压实雪 - 极地道路)
•	Material_Snow_Contaminated (污染雪 - 核冬天)
•	Material_Permafrost (永久冻土 - 极地地区)

【水体/液体材质】
•	Material_Water_Shallow (浅水 - 被淹道路)
•	Material_Water_Toxic (有毒积水 - 化学污染)
•	Material_Oil_Spilled (溢油 - 工业事故)
•	Material_Acid_Corrosive (腐蚀性酸液 - 化学武器)
•	Material_Blood_Coagulated (凝固血液 - 大屠杀现场)

【特殊环境材质】
•	Material_Ash_Volcanic (火山灰 - 火山区域)
•	Material_Ash_Nuclear (核灰 - 核爆现场)
•	Material_Glass_Melted (熔化玻璃 - 高温爆炸)
•	Material_Debris_Mixed (混合残骸 - 爆炸废墟)
•	Material_Bone_Crushed (碎骨 - 丧尸聚集地)
•	Material_Slime_Toxic (有毒粘液 - 变异生物分泌)

1.3 宽度/车道 (Width/Lanes):

【标准车道配置】
•	Width_SingleLane (单车道 - 乡村小路)
•	Width_DoubleLane (双车道 - 一般公路)
•	Width_MultiLane_Three (三车道 - 城市主干道)
•	Width_MultiLane_FourPlus (四车道及以上 - 高速公路)
•	Width_Narrow (狭窄，< 标准单车道 - 小巷/应急通道)
•	Width_Wide (宽阔，> 标准多车道 - 机场跑道)

【末日特殊宽度】
•	Width_Variable_DueToDamage (因损坏导致宽度可变 - 爆炸/坍塌)
•	Width_Variable_DueToDamage_Extreme (因极端损坏导致宽度多变 - 战争废墟)
•	Width_Blocked_Partial (部分阻塞 - 废弃车辆/路障)
•	Width_Blocked_Severe (严重阻塞 - 大量障碍物)
•	Width_Expanded_Emergency (紧急拓宽 - 疏散路线)
•	Width_Compressed_Defensive (防御性收窄 - 检查站/要塞入口)

1.4 状况/状态 (Condition/State):

【基础道路状态】
•	State_Intact (完好 - 方舟城等高级区域)
•	State_Intact_Clean (完好且洁净 - 方舟城内部)
•	State_Intact_Pristine (完好无损且原始 - AI工厂)
•	State_Weathered_Light (轻微风化 - 一般废弃道路)
•	State_Weathered_Heavy (严重风化 - 长期废弃)

【裂纹/破损状态】
•	State_Cracked_Minor (轻微龟裂 - 老化道路)
•	State_Cracked_Major (严重龟裂 - 地震/爆炸影响)
•	State_Cracked_Extreme_Bridge (桥面极端破裂 - 结构失效)
•	State_Cracked_IceFissure_Deep (深邃冰裂缝 - 极地环境)
•	State_Potholed (有坑洞 - 维护不良)
•	State_Potholed_Extreme_Craters (大量巨型弹坑 - 战争区域)
•	State_Potholed_Massive_BridgeGap (桥面巨大坑洞/断裂 - 桥梁损毁)

【地形变化状态】
•	State_Uneven (不平整/隆起 - 地质活动)
•	State_Uneven_VolcanicActivity (火山活动导致不平整 - 火山区)
•	State_Sloped_Extreme_Ruin (废墟中极度倾斜的路面 - 建筑坍塌)
•	State_Sinking_Subsidence (下沉/塌陷 - 地基不稳)
•	State_Uplifted_Geological (地质抬升 - 地震后果)

【植被覆盖状态】
•	State_Overgrown_Edges (边缘长草 - 轻度荒废)
•	State_Overgrown_Full (大面积长草/被植被覆盖 - 重度荒废)
•	State_Overgrown_MutatedFlora (变异植物覆盖 - 生物污染区)
•	State_Overgrown_Fungal (真菌覆盖 - 真菌感染区)

【水体影响状态】
•	State_Flooded_Shallow (浅积水 - 排水不良)
•	State_Flooded_Deep (深积水 - 严重水灾)
•	State_Flooded_Tidal_Intermittent (潮汐间歇性淹没 - 海岸道路)
•	State_Muddy_DueToRain (雨后泥泞 - 乡村土路)
•	State_WaterDamaged_Erosion (水蚀损坏 - 长期浸泡)

【冰雪/极地状态】
•	State_Icy (结冰 - 冬季/极地)
•	State_Icy_ExtremeSlickness (极度湿滑结冰 - 冰河路)
•	State_SnowCovered (积雪覆盖 - 冬季道路)
•	State_SnowCovered_Heavy (厚雪覆盖 - 暴雪后)
•	State_FrostCovered (覆盖霜雪 - 极地建筑)
•	State_Frozen_Permafrost (永久冻结 - 极地地区)

【污染/化学状态】
•	State_Sandy (积沙覆盖 - 沙漠道路)
•	State_SandCovered_Partial (部分被沙掩埋 - 沙漠建筑)
•	State_RadiationDustCovered (覆盖放射性尘埃 - 核污染区)
•	State_Corroded_Chemical (化学腐蚀 - 工业区)
•	State_Contaminated_Biological (生物污染 - 病毒泄漏区)
•	State_Contaminated_Toxic (有毒污染 - 化学武器区)

【火灾/爆炸状态】
•	State_Burnt (燃烧痕迹 - 火灾现场)
•	State_Burnt_WidespreadFires (大面积燃烧 - 逃离场景)
•	State_Scorched_Nuclear (核爆烧焦 - 核爆现场)
•	State_Melted_HighHeat (高温熔化 - 火山/爆炸区)
•	State_Vitrified_WeaponTest (玻璃化 - 武器试验场)

【战争/暴力状态】
•	State_Bloodstained (血迹斑斑 - 战斗现场)
•	State_Bloodstained_Massive (大面积血迹 - 大屠杀现场)
•	State_ImpactDamage_Vehicle (车辆撞击损坏 - 交通事故)
•	State_ImpactDamage_Weapon (武器撞击损坏 - 战争废墟)
•	State_Crushed_ByMegaVehicle (被巨型载具碾压变形 - 重型机械)
•	State_Shelled_Artillery (炮击损坏 - 战争区域)

【阻塞/障碍状态】
•	State_RubbleCovered (覆盖瓦砾 - 建筑坍塌)
•	State_Blocked_Vehicles_Heavy (被大量车辆严重堵塞 - 交通瘫痪)
•	State_Blocked_Vehicles_Partial (被部分车辆堵塞 - 轻度拥堵)
•	State_Blocked_Barricades_Heavy (被重型路障堵塞 - 军事封锁)
•	State_Blocked_Debris_Massive (被大量残骸堵塞 - 爆炸后果)
•	State_Landslide_Blocked (塌方堵塞 - 盘山公路)

【结构失效状态】
•	State_Collapsed_Partial_Tunnel (隧道部分塌方 - 结构失效)
•	State_Collapsing_Road (道路正在崩塌 - 逃离场景)
•	State_Bridge_MisalignedJoint (桥梁连接处错位 - 地震影响)
•	State_Bridge_CollapsedSection (桥梁部分断裂/坍塌 - 结构失效)
•	State_Tunnel_VentilationFailure (隧道通风失效 - 系统故障)

【特殊末日状态】
•	State_OilStained (有油污 - 加油站，修车厂)
•	State_Surface_OilySlick_PlatformDeck (平台甲板油腻湿滑 - 工业平台)
•	State_Track_Broken_Twisted_Maglev (磁悬浮轨道断裂扭曲 - 高科技废墟)
•	State_Damage_NaniteDecomposition_Partial (纳米机器人部分分解 - AI区域)
•	State_Damage_Heavy_StructuralCollapse_Mega (巨型结构严重坍塌 - 太空电梯)
•	State_Building_Mobile_Modular (模块化可移动建筑 - 移动城市)

1.5 特征/组件 (Features/Components):

【基础道路设施】
•	Feature_Sidewalk_Left (左侧人行道 - 城市街道)
•	Feature_Sidewalk_Right (右侧人行道 - 城市街道)
•	Feature_Sidewalk_Both (两侧人行道 - 主要街道)
•	Feature_NoSidewalk (无人行道 - 乡村道路)
•	Feature_Curb_High (高路缘 - 城市道路)
•	Feature_Curb_Low (低路缘 - 住宅区)
•	Feature_Curb_Damaged (损坏路缘 - 废弃道路)
•	Feature_Curb_Bloodstained (血迹路缘 - 丧尸袭击现场)

【护栏/隔离设施】
•	Feature_Guardrail_Metal (金属护栏 - 高速公路)
•	Feature_Guardrail_Concrete (混凝土护栏 - 桥梁)
•	Feature_Guardrail_Damaged (损坏护栏 - 事故现场)
•	Feature_Guardrail_Bloodstained (血迹护栏 - 丧尸攻击)
•	Feature_MedianStrip_Grass (中央草地隔离带 - 城市主干道)
•	Feature_MedianStrip_Concrete (中央混凝土隔离带 - 高速公路)
•	Feature_MedianStrip_Overgrown (杂草丛生隔离带 - 废弃道路)
•	Feature_MedianStrip_Destroyed (被毁隔离带 - 战争废墟)

【交叉路口】
•	Feature_Intersection_Cross (十字交叉口 - 城市街道)
•	Feature_Intersection_T (T字交叉口 - 支路连接)
•	Feature_Intersection_Y (Y字交叉口 - 分岔路)
•	Feature_Intersection_Roundabout (环岛 - 交通枢纽)
•	Feature_Intersection_Damaged (损坏交叉口 - 爆炸/坍塌)
•	Feature_Intersection_Blocked (阻塞交叉口 - 路障/废车)

【隧道/桥梁设施】
•	Feature_Tunnel_Entrance (隧道入口 - 山体/地下)
•	Feature_Tunnel_Exit (隧道出口 - 山体/地下)
•	Feature_Tunnel_Entrance_Collapsed (坍塌隧道入口 - 结构失效)
•	Feature_Tunnel_Emergency_Exit (隧道紧急出口 - 安全设施)
•	Feature_Bridge_Approach (桥梁引桥 - 接近段)
•	Feature_Bridge_Span (桥梁主体 - 跨越段)
•	Feature_Bridge_Span_CollapsedSections (桥梁主体垮塌段 - 结构损毁)
•	Feature_Bridge_Pier_Damaged (损坏桥墩 - 结构问题)

【排水/维护设施】
•	Feature_DrainageGrate (排水格栅 - 城市道路)
•	Feature_DrainageGrate_Clogged (堵塞排水格栅 - 维护不良)
•	Feature_DrainageGrate_Broken (破损排水格栅 - 损坏设施)
•	Feature_ManholeCover (窨井盖 - 地下设施入口)
•	Feature_ManholeCover_Missing (丢失窨井盖 - 危险区域)
•	Feature_ManholeCover_Bloodstained (血迹窨井盖 - 丧尸藏身处)

【军事/防御设施】
•	Feature_Roadblock_Military_Sandbags (军事路障 - 沙袋 - 检查站)
•	Feature_Roadblock_Military_Concrete (军事路障 - 混凝土块 - 防御工事)
•	Feature_Roadblock_Makeshift_Containers (临时路障 - 集装箱 - 紧急封锁)
•	Feature_Roadblock_Abandoned (废弃路障 - 失效防线)
•	Feature_Checkpoint_Abandoned (废弃检查站 - 军事撤离)
•	Feature_Minefield_MarkedOrUnmarked (雷区 - 有或无标记 - 军事禁区)
•	Feature_TankTrap_Concrete (混凝土反坦克障碍 - 防御工事)
•	Feature_BarbedWire_Coil (蛇腹形带刺铁丝网 - 军事封锁)

【交通控制设施】
•	Feature_TrafficLight_Working (正常交通信号灯 - 方舟城等)
•	Feature_TrafficLight_Damaged (损坏交通信号灯 - 废弃城市)
•	Feature_TrafficLight_Flickering (闪烁交通信号灯 - 电力不稳)
•	Feature_TrafficLight_Dark (熄灭交通信号灯 - 停电区域)
•	Feature_StopSign_Intact (完好停止标志 - 乡村路口)
•	Feature_StopSign_Damaged (损坏停止标志 - 废弃路口)
•	Feature_SpeedBump_Asphalt (沥青减速带 - 住宅区)
•	Feature_SpeedBump_Damaged (损坏减速带 - 废弃道路)

【服务设施】
•	Feature_TollBooth_Ruined (收费站废墟 - 高速公路)
•	Feature_ServiceArea_Highway_Abandoned (废弃高速服务区 - 补给点)
•	Feature_RestStop_Abandoned (废弃休息站 - 旅行者遗迹)
•	Feature_WeighStation_Abandoned (废弃称重站 - 货运检查)
•	Feature_GasStation_Canopy (加油站遮雨棚 - 燃料补给)
•	Feature_GasStation_Pumps_Destroyed (被毁加油泵 - 爆炸现场)

【铁路交叉设施】
•	Feature_RailwayCrossing_Road (铁路公路交叉口 - 危险路段)
•	Feature_RailwayCrossing_Damaged (损坏铁路交叉口 - 事故现场)
•	Feature_RailwayTrack_Tunnel (隧道内铁轨 - 地下交通)
•	Feature_RailwayTrack_Monorail_Elevated (高架单轨铁路 - 方舟城)
•	Feature_RailwayTrack_Maglev_Elevated_Ruined (废弃高架磁悬浮轨道 - 高科技废墟)

【特殊末日设施】
•	Feature_OverheadGantrySign_Highway (高速公路龙门架指示牌 - 导航)
•	Feature_OverheadGantrySign_Damaged (损坏龙门架 - 废弃高速)
•	Feature_EmergencyRamp_Steep (紧急避险坡道 - 盘山路/高速)
•	Feature_ReflectiveRoadStud (路面反光道钉 - 夜间导航)
•	Feature_ReflectiveRoadStud_Missing (缺失反光道钉 - 维护不良)
•	Feature_Walkway_Maintenance_Tunnel (隧道维修通道 - 工程设施)

【高科技/未来设施】
•	Feature_ElevatorPlatform_Vehicle_Large (大型车辆升降平台 - 方舟城入口)
•	Feature_TestPlatform_Outdoor_Scorched (露天试验平台 - 烧灼 - 武器试验)
•	Feature_Helipad_Platform (平台直升机场 - 空中交通)
•	Feature_Helipad_Damaged (损坏直升机场 - 废弃设施)
•	Feature_LaunchPad_Rocket (火箭发射台 - 航天设施)
•	Feature_LaunchPad_Abandoned (废弃发射台 - 太空计划遗迹)

【海上/水上设施】
•	Feature_Walkway_OilRig_InterConnector_Bridge (石油钻井平台连接桥 - 海上设施)
•	Feature_Cableway_OilRig_InterConnector (石油钻井平台间缆索 - 运输系统)
•	Feature_Dock_Loading (装卸码头 - 货物运输)
•	Feature_Dock_Damaged (损坏码头 - 海岸废墟)
•	Feature_Pier_Fishing (渔业码头 - 海岸生活)
•	Feature_Pier_Collapsed (坍塌码头 - 结构失效)

【生存者改造设施】
•	Feature_Barricade_Improvised (临时路障 - 生存者建造)
•	Feature_Watchtower_Makeshift (临时瞭望塔 - 防御哨所)
•	Feature_Spikes_AntiVehicle (反车辆尖刺 - 防御工事)
•	Feature_TireWall_Defensive (轮胎防御墙 - 废料利用)
•	Feature_ScrapMetal_Barrier (废金属屏障 - 拾荒者建造)
•	Feature_Sandbag_Fortification (沙袋工事 - 临时防御)

1.6 标线 (Markings):

【基础道路标线】
•	Marking_CenterLine_Solid_Single (单实中线 - 标准道路)
•	Marking_CenterLine_Solid_Double (双实中线 - 禁止超车)
•	Marking_CenterLine_Dashed_Single (单虚中线 - 允许超车)
•	Marking_CenterLine_SolidDashed (一实一虚中线 - 单向超车)
•	Marking_LaneLine_Solid (车道实线 - 禁止变道)
•	Marking_LaneLine_Dashed (车道虚线 - 允许变道)
•	Marking_EdgeLine_Solid (边缘实线 - 道路边界)

【交通标线】
•	Marking_PedestrianCrossing (人行横道线 - 行人通道)
•	Marking_PedestrianCrossing_Faded (褪色人行横道 - 废弃道路)
•	Marking_PedestrianCrossing_Bloodstained (血迹人行横道 - 丧尸袭击现场)
•	Marking_StopLine (停止线 - 交叉路口)
•	Marking_StopLine_Worn (磨损停止线 - 老化道路)
•	Marking_YieldLine (让行线 - 支路汇入)

【方向指示标线】
•	Marking_Arrow_Straight (直行箭头 - 车道指示)
•	Marking_Arrow_Turn_Left (左转箭头 - 转向车道)
•	Marking_Arrow_Turn_Right (右转箭头 - 转向车道)
•	Marking_Arrow_UTurn (掉头箭头 - 掉头车道)
•	Marking_Arrow_Multiple (多方向箭头 - 复合车道)

【特殊标线】
•	Marking_ParkingSpaceLines (停车位标线 - 地下车库/停车场)
•	Marking_ParkingSpaceLines_Faded (褪色停车位标线 - 废弃停车场)
•	Marking_LoadingZone (装卸区标线 - 货运区域)
•	Marking_BusLane (公交专用道标线 - 城市交通)
•	Marking_BikeLane (自行车道标线 - 绿色出行)
•	Marking_Handicap (残疾人停车标线 - 无障碍设施)

【末日特殊标线】
•	Marking_NoMarkings (无标线 - 乡村道路/废弃区域)
•	Marking_Faded (标线褪色 - 长期无维护)
•	Marking_Faded_Severe (严重褪色 - 几乎不可见)
•	Marking_Bloodstained (血迹覆盖标线 - 丧尸袭击现场)
•	Marking_Burned (烧毁标线 - 火灾现场)
•	Marking_Scraped_Off (刮除标线 - 人为破坏)
•	Marking_Covered_Debris (残骸覆盖标线 - 爆炸现场)

【临时/紧急标线】
•	Marking_Temporary_Paint (临时油漆标线 - 应急修复)
•	Marking_Chalk_Makeshift (粉笔临时标记 - 生存者标识)
•	Marking_Spray_Graffiti (喷漆涂鸦 - 帮派标记)
•	Marking_Blood_Trail (血迹痕迹 - 拖拽尸体)
•	Marking_Tire_Skid (轮胎刹车痕 - 紧急制动)
•	Marking_Explosion_Scorch (爆炸烧焦痕迹 - 战斗现场)

【高科技标线】
•	Marking_LED_Embedded (嵌入式LED标线 - 方舟城高科技道路)
•	Marking_Holographic_Projection (全息投影标线 - 虚拟城市)
•	Marking_Energy_Glowing (发光能量标线 - 超自然区域)
•	Marking_Nanite_SelfRepairing (纳米自修复标线 - AI控制区)

1.7 交通流向 (TrafficFlow - 逻辑标签):

【基础流向】
•	Flow_OneWay (单向 - 城市单行道)
•	Flow_TwoWay (双向 - 标准公路)
•	Flow_Reversible (可逆车道 - 潮汐车道)
•	Flow_Blocked (阻断 - 路障封锁)
•	Flow_Restricted (限制通行 - 军事管制)

【末日特殊流向】
•	Flow_Evacuation_OneWay (单向疏散 - 紧急撤离)
•	Flow_Contraflow_Emergency (逆向应急 - 灾难疏散)
•	Flow_Patrol_Military (军事巡逻 - 戒严状态)
•	Flow_Survivor_Convoy (生存者车队 - 集体行动)
•	Flow_Zombie_Horde (丧尸群移动 - 威胁区域)
•	Flow_Abandoned_NoTraffic (废弃无交通 - 死寂区域)

1.8 环境上下文 (EnvironmentContext - 逻辑标签):

【第一阶段：都市逃亡环境】
•	Context_Urban_Core_Burning (燃烧的城市核心区 - 主题01)
•	Context_Urban_Core_Abandoned (废弃城市核心区 - 主题01)
•	Context_Underground_Garage_Flooded (被淹地下车库 - 主题02)
•	Context_Underground_Garage_Emergency (紧急地下车库 - 主题02)
•	Context_Overpass_Damaged_Structural (结构损坏高架桥 - 主题03)
•	Context_Overpass_Collapsed_Partial (部分坍塌高架桥 - 主题03)
•	Context_Tunnel_Mountain_Blocked (山体隧道阻塞 - 主题04)
•	Context_Tunnel_Emergency_Escape (隧道紧急逃生 - 主题04)
•	Context_Subway_Abandoned_Dark (废弃黑暗地铁 - 主题05)
•	Context_Subway_Flooded_Dangerous (危险被淹地铁 - 主题05)
•	Context_Highway_Checkpoint_Military (高速军事检查站 - 主题06)
•	Context_Highway_Checkpoint_Overrun (被攻陷检查站 - 主题06)
•	Context_Industrial_Loading_Dock (工业装卸码头 - 主题07)
•	Context_Industrial_Warehouse_Maze (工业仓库迷宫 - 主题07)
•	Context_Residential_Suburban_Quiet (安静郊区住宅 - 主题08)
•	Context_Residential_Suburban_Chaos (混乱郊区住宅 - 主题08)
•	Context_Commercial_Shopping_Looted (被洗劫购物区 - 主题09)
•	Context_Commercial_Shopping_Barricaded (路障购物区 - 主题09)
•	Context_Airport_Service_Abandoned (废弃机场服务区 - 主题10)
•	Context_Airport_Runway_Emergency (机场跑道紧急区 - 主题10)
•	Context_Waterfront_Dock_Evacuation (滨水码头疏散区 - 主题11)
•	Context_Waterfront_Marina_Abandoned (废弃游艇码头 - 主题11)
•	Context_Park_Urban_Overgrown (城市公园杂草丛生 - 主题12)
•	Context_Park_Refugee_Camp (公园难民营 - 主题12)
•	Context_Mountain_Road_Treacherous (山路险峻路段 - 主题13)
•	Context_Mountain_Road_Landslide (山路塌方路段 - 主题13)
•	Context_Warehouse_Logistics_Hub (物流仓库枢纽 - 主题14)
•	Context_Warehouse_Storage_Maze (仓储迷宫区域 - 主题14)

【第二阶段：末日公路环境】
•	Context_Desert_Highway_Endless (无尽沙漠高速 - 主题15)
•	Context_Desert_Oasis_Mirage (沙漠绿洲幻象 - 主题15)
•	Context_Coastal_Scenic_Storm (暴风雨海岸路 - 主题16)
•	Context_Coastal_Cliff_Dangerous (危险海岸悬崖 - 主题16)
•	Context_Forest_Dense_Overgrown (茂密森林道路 - 主题17)
•	Context_Forest_Logging_Abandoned (废弃伐木区 - 主题17)
•	Context_Mountain_Pass_Snowy (雪山山口通道 - 主题18)
•	Context_Mountain_Peak_Observatory (山顶观测站 - 主题18)
•	Context_Farmland_Abandoned_Crops (废弃农田作物 - 主题19)
•	Context_Farmland_Barn_Shelter (农场谷仓避难所 - 主题19)
•	Context_GasStation_Highway_Last (高速最后加油站 - 主题20)
•	Context_GasStation_Explosion_Site (加油站爆炸现场 - 主题20)

【生存者定居点环境】
•	Context_Settlement_Scavenger_Fortified (拾荒者要塞定居点 - 主题21)
•	Context_Settlement_Trader_Hub (商人贸易枢纽 - 主题22)
•	Context_Settlement_Survivor_Community (生存者社区 - 主题22)
•	Context_Settlement_Abandoned_Ghost (废弃幽灵定居点 - 主题22)

【军事/政府设施环境】
•	Context_Military_Base_Secured (安全军事基地 - 主题23)
•	Context_Military_Base_Overrun (被攻陷军事基地 - 主题23)
•	Context_PowerPlant_Nuclear_Active (运行中核电站 - 主题24)
•	Context_PowerPlant_Nuclear_Meltdown (核电站熔毁 - 主题24)
•	Context_Dam_Hydroelectric_Functional (功能性水电站 - 主题25)
•	Context_Dam_Structural_Failure (大坝结构失效 - 主题25)

【方舟城环境】
•	Context_ArkCity_Approach_Checkpoint (方舟城接近检查站 - 主题26)
•	Context_ArkCity_Outer_Defenses (方舟城外围防御 - 主题26)
•	Context_ArkCity_Inner_Sanctuary (方舟城内部圣域 - 主题27)
•	Context_ArkCity_Underground_Network (方舟城地下网络 - 主题28)

【特殊试验环境】
•	Context_WeaponTest_Desert_Range (沙漠武器试验场 - 主题29)
•	Context_WeaponTest_Underground_Bunker (地下武器试验掩体 - 主题29)
•	Context_Volcano_Observatory_Active (活火山观测站 - 主题30)
•	Context_Volcano_Evacuation_Zone (火山疏散区 - 主题30)

【研究设施环境】
•	Context_Research_Campus_Pharmaceutical (制药研究园区 - 主题31)
•	Context_Research_Lab_Abandoned (废弃研究实验室 - 主题31)
•	Context_Desert_BuriedCity_Archaeological (沙漠古城考古区 - 主题32)
•	Context_Desert_Sandstorm_Hazard (沙漠沙尘暴危险区 - 主题32)

【高科技设施环境】
•	Context_Platform_Research_Offshore (离岸研究平台 - 主题33)
•	Context_Platform_Oil_Drilling (海上石油钻井平台 - 主题33)
•	Context_SpaceElevator_Base_Operational (运行中太空电梯基地 - 主题34)
•	Context_SpaceElevator_Debris_Field (太空电梯残骸场 - 主题34)
•	Context_Factory_AI_Automated (AI自动化工厂 - 主题35)
•	Context_Factory_Production_Sterile (无菌生产工厂 - 主题35)

【自然奇观环境】
•	Context_TreeCanopy_Giant_Living (巨型活体树冠 - 主题36)
•	Context_TreeCanopy_Village_Primitive (树冠原始村落 - 主题36)
•	Context_FloatingStructure_SciFi_Ruins (科幻浮空结构废墟 - 主题37)
•	Context_FloatingStructure_Energy_Anomaly (浮空结构能量异常 - 主题37)

【纪念与希望环境】
•	Context_Memorial_Disaster_Monument (灾难纪念碑 - 主题38)
•	Context_Memorial_Hope_Beacon (希望灯塔 - 主题38)
•	Context_Settlement_New_Hopeful (充满希望的新定居点 - 主题38)

【巨型结构环境】
•	Context_Bridge_Megastructure_Intact (完整巨型桥梁 - 主题39)
•	Context_Bridge_Megastructure_Collapsed (坍塌巨型桥梁 - 主题39)
•	Context_SaltFlats_Crystalline_Vast (广阔结晶盐碱地 - 主题40)
•	Context_SaltFlats_Mining_Abandoned (废弃盐矿开采区 - 主题40)

【异常生态环境】
•	Context_FungalForest_Spore_Toxic (有毒孢子真菌森林 - 主题41)
•	Context_FungalForest_Bioluminescent (生物发光真菌森林 - 主题41)
•	Context_Coastal_Shipgraveyard_Vast (广阔海岸沉船墓地 - 主题42)
•	Context_Coastal_Lighthouse_Ruined (废弃海岸灯塔 - 主题42)

【文化设施环境】
•	Context_Museum_Exhibition_Preserved (保存完好的博物馆展厅 - 主题43)
•	Context_Museum_Looted_Vandalized (被洗劫破坏的博物馆 - 主题43)
•	Context_Ship_Cruise_Beached (搁浅游轮 - 主题44)
•	Context_Ship_Military_Carrier (军用航空母舰 - 主题44)

【种子银行环境】
•	Context_SeedBank_Underground_Secure (地下安全种子银行 - 主题44)
•	Context_SeedBank_Genetic_Archive (基因档案库 - 主题44)

【总结说明】
以上标签系统为末日丧尸游戏的室外场景程序化建模提供了全面的分类体系。每个标签都与具体的游戏章节主题相关联，确保生成的场景能够准确反映当前的故事进展和氛围需求。

程序化生成系统可以根据当前游戏阶段、章节主题、威胁等级等参数，智能选择合适的道路类型、材质、状态、特征等标签组合，创造出符合末日氛围且具有叙事意义的道路网络环境。

【使用建议】
1. 根据当前章节主题选择对应的道路类型和环境上下文
2. 结合游戏进度调整道路状态和损坏程度
3. 考虑丧尸威胁等级来配置防御设施和路障
4. 利用材质和标线的组合来营造不同的视觉效果
5. 通过特征组件的搭配来增加场景的细节和真实感

此标签系统将为玩家提供丰富多样、符合剧情发展的道路驾驶体验，增强游戏的沉浸感和叙事表现力。
•	State_SnowCovered (积雪覆盖)
•	State_Sandy (积沙覆盖)
•	State_Burnt (燃烧痕迹)
•	State_RubbleCovered (覆盖瓦砾)
•	State_Blocked_Vehicles_Heavy (被大量车辆严重堵塞)
•	State_Blocked_Vehicles_Partial (被部分车辆堵塞)
•	State_OilStained (有油污 - 加油站，修车厂)
•	State_Muddy_DueToRain (雨后泥泞 - 乡村土路)
•	State_Landslide_Blocked (塌方堵塞 - 盘山公路)
•	State_Bridge_MisalignedJoint (桥梁连接处错位)
•	State_Bridge_CollapsedSection (桥梁部分断裂/坍塌)
•	State_Tunnel_VentilationFailure (隧道通风失效)
•	State_RadiationDustCovered (覆盖放射性尘埃)
•	State_Collapsed_Partial_Tunnel (隧道部分塌方)
•	State_Potholed_Extreme_Craters (大量巨型弹坑)
•	State_Intact_Clean (完好且洁净 - 方舟城)
•	State_Blocked_Barricades_Heavy (被重型路障堵塞)
•	State_Collapsing_Road (道路正在崩塌 - 逃离场景)
•	State_Burnt_WidespreadFires (大面积燃烧 - 逃离场景)
•	State_Uneven_VolcanicActivity (火山活动导致不平整)
•	State_Cracked_Extreme_Bridge (桥面极端破裂)
•	State_Potholed_Massive_BridgeGap (桥面巨大坑洞/断裂)
•	State_Sloped_Extreme_Ruin (废墟中极度倾斜的路面)
•	State_Icy_ExtremeSlickness (极度湿滑结冰 - 冰河路)
•	State_Crack_IceFissure_Deep (深邃冰裂缝)
•	State_Crushed_ByMegaVehicle (被巨型载具碾压变形)
•	State_Intact_Pristine (完好无损且原始 - AI工厂)
•	State_Flooded_Tidal_Intermittent (潮汐间歇性淹没)
•	State_Track_Broken_Twisted_Maglev (磁悬浮轨道断裂扭曲)
•	State_Surface_OilySlick_PlatformDeck (平台甲板油腻湿滑)
1.5 特征/组件 (Features/Components):
•	Feature_Sidewalk_Left (左侧人行道)
•	Feature_Sidewalk_Right (右侧人行道)
•	Feature_Sidewalk_Both (两侧人行道)
•	Feature_NoSidewalk (无人行道)
•	Feature_Curb_High (高路缘)
•	Feature_Curb_Low (低路缘)
•	Feature_Guardrail_Metal (金属护栏)
•	Feature_Guardrail_Concrete (混凝土护栏)
•	Feature_MedianStrip_Grass (中央草地隔离带)
•	Feature_MedianStrip_Concrete (中央混凝土隔离带)
•	Feature_Intersection_Cross (十字交叉口)
•	Feature_Intersection_T (T字交叉口)
•	Feature_Intersection_Y (Y字交叉口)
•	Feature_Intersection_Roundabout (环岛)
•	Feature_Tunnel_Entrance (隧道入口)
•	Feature_Tunnel_Exit (隧道出口)
•	Feature_Bridge_Approach (桥梁引桥)
•	Feature_Bridge_Span (桥梁主体)
•	Feature_DrainageGrate (排水格栅)
•	Feature_Roadblock_Military_Sandbags (军事路障 - 沙袋)
•	Feature_Roadblock_Military_Concrete (军事路障 - 混凝土块)
•	Feature_Roadblock_Makeshift_Containers (临时路障 - 集装箱)
•	Feature_TollBooth_Ruined (收费站废墟)
•	Feature_RailwayCrossing_Road (铁路公路交叉口)
•	Feature_ServiceArea_Highway_Abandoned (废弃高速服务区)
•	Feature_OverheadGantrySign_Highway (高速公路龙门架指示牌)
•	Feature_EmergencyRamp_Steep (紧急避险坡道 - 盘山路/高速)
•	Feature_ReflectiveRoadStud (路面反光道钉)
•	Feature_SpeedBump_Asphalt (沥青减速带)
•	Feature_RailwayTrack_Tunnel (隧道内铁轨)
•	Feature_Walkway_Maintenance_Tunnel (隧道维修通道)
•	Feature_Minefield_MarkedOrUnmarked (雷区 - 有或无标记)
•	Feature_ElevatorPlatform_Vehicle_Large (大型车辆升降平台 - 方舟城入口)
•	Feature_RailwayTrack_Monorail_Elevated (高架单轨铁路 - 方舟城)
•	Feature_TestPlatform_Outdoor_Scorched (露天试验平台 - 烧灼)
•	Feature_Bridge_Span_CollapsedSections (桥梁主体垮塌段)
•	Feature_Helipad_Platform (平台直升机场)
•	Feature_RailwayTrack_Maglev_Elevated_Ruined (废弃高架磁悬浮轨道)
•	Feature_Walkway_OilRig_InterConnector_Bridge (石油钻井平台连接桥)
•	Feature_Cableway_OilRig_InterConnector (石油钻井平台间缆索)
1.6 标线 (Markings):
•	Marking_CenterLine_Solid_Single (单实中线)
•	Marking_CenterLine_Solid_Double (双实中线)
•	Marking_CenterLine_Dashed_Single (单虚中线)
•	Marking_CenterLine_SolidDashed (一实一虚中线)
•	Marking_LaneLine_Solid (车道实线)
•	Marking_LaneLine_Dashed (车道虚线)
•	Marking_EdgeLine_Solid (边缘实线)
•	Marking_PedestrianCrossing (人行横道线)
•	Marking_StopLine (停止线)
•	Marking_Arrow_Straight (直行箭头)
•	Marking_Arrow_Turn (转向箭头)
•	Marking_NoMarkings (无标线)
•	Marking_Faded (标线褪色)
•	Marking_ParkingSpaceLines (停车位标线 - 地下车库/停车场)
1.7 交通流向 (TrafficFlow - 逻辑标签):
•	Flow_OneWay (单向)
•	Flow_TwoWay (双向)
1.8 环境上下文 (EnvironmentContext - 逻辑标签):
•	Context_Urban_Core (城市核心区)
•	Context_Urban_Residential (城市居民区)
•	Context_Urban_Underground_Parking (城市地下停车场)
•	Context_Urban_Overpass (城市高架桥)
•	Context_Urban_Outskirts_Checkpoint (城市郊区检查站)
•	Context_Urban_Industrial_RepairShop (城市工业区修车厂)
•	Context_Suburban (郊区)
•	Context_Rural_Settlement (乡村聚落)
•	Context_Rural_Wilderness (乡村荒野)
•	Context_Rural_GasStation (乡村公路加油站)
•	Context_Highway_Interstate (州际高速公路)
•	Context_Urban_SewerSystem (城市下水道系统 - 入口/出口周边)
•	Context_Urban_Rooftop_Parking (城市屋顶停车场/平台)
•	Context_Urban_BusTerminal (城市公交总站)
•	Context_Urban_RiverfrontPark (城市河滨公园)
•	Context_Mountain_WindingRoad (山区盘山公路)
•	Context_Industrial_LogisticsHub (工业物流中转仓库区)
•	Context_Desert_Road (沙漠公路)
•	Context_ForestRoad_Dense (茂密森林道路)
•	Context_RiverGorge_Road (河谷险路)
•	Context_Farmland_Road (农田间道路)
•	Context_Tunnel_Mountain (山体隧道)
•	Context_Wasteland_Camp_Perimeter (废土营地周边)
•	Context_IndustrialZone_Abandoned (废弃工业区)
•	Context_Tundra_SnowyRoad (冰雪冻原道路)
•	Context_Highway_AmbushPoint (高速公路伏击点 - 掠夺者)
•	Context_CoastalRoad_Stormy (暴风雨沿海道路)
•	Context_UrbanFringe_SupplyRoute (城市废墟边缘补给线)
•	Context_Bridge_Broken_Chasm (断桥飞跃点)
•	Context_RadiationZone_Road (辐射区道路)
•	Context_Wasteland_Settlement_Fortified (废土定居点 - 铁骑议会)
•	Context_UndergroundHighway_Entrance (地下高速公路入口周边)
•	Context_Bridge_SeaCrossing_Damaged (跨海大桥 - 部分幸存)
•	Context_Entertainment_OutdoorCinema (废弃露天汽车影院)
•	Context_Park_National_VisitorArea (国家公园游客中心区域)
•	Context_Quarry_RoadNetwork (采石场道路网络)
•	Context_Coastal_FishingVillage (沿海渔村)
•	Context_Remote_CommunicationStation (偏远军事通讯基站)
•	Context_Dam_Roadway (大型水坝坝顶公路)
•	Context_Wasteland_Marketplace (拾荒者之城集市)
•	Context_Geothermal_PlantRoad (地热发电区域道路)
•	Context_Vineyard_EstateRoads (葡萄酒庄园道路)
•	Context_Aerospace_LaunchComplex_Roads (航天发射中心道路)
•	Context_Industrial_Factory_Perimeter (工厂外围)
•	Context_Urban_Plaza (城市广场 - 体育馆/博物馆前)
•	Context_Coastal_WreckSite_Ship (搁浅船只残骸点)
•	Context_Remote_SecureFacility_Entrance (偏远安全设施入口 - 地下种子银行)
•	Context_Urban_Underground_Network (城市地下交通/设施网络)
•	Context_Industrial_OpenPitMine_BossArena (露天矿场Boss战区)
•	Context_Urban_StadiumRuin_BossArena (城市体育场废墟Boss战区)
•	Context_Cave_Underground_BossArena (地下洞窟Boss战区)
•	Context_Military_Base_Fortified (军事禁区/要塞)
•	Context_Industrial_PowerPlant_Hazardous (危险的能源厂区)
•	Context_FortifiedCity_OuterDefenses_KillZone (方舟城外围防御/死亡地带)
•	Context_Urban_PlannedCity_HighSecurityZone (方舟城内高戒备区)
•	Context_Urban_PlannedCity_SlumDistrict (方舟城内贫民窟/黑市)
•	Context_Remote_WeaponTestSite_Desert (沙漠武器试验场)
•	Context_Remote_WeaponTestSite_Island (海岛武器试验场)
•	Context_Remote_WeaponTestSite_Underground (地下武器试验场)
•	Context_BossLair_UrbanLandmark_Modified (Boss巢穴 - 改造的城市地标)
•	Context_BossLair_MilitaryFortress (Boss巢穴 - 军事要塞)
•	Context_BossLair_UndergroundCaveNetwork (Boss巢穴 - 地下洞穴网)
•	Context_Arena_BossBattle_Specific (特定Boss战竞技场)
•	Context_Siege_SurvivorHoldout_Exterior (幸存者据点外围围攻战)
•	Context_Escape_NuclearMeltdown (逃离 - 核电站熔毁)
•	Context_Escape_VolcanicEruption (逃离 - 火山爆发)
•	Context_Escape_CityBombing (逃离 - 城市被轰炸)
•	Context_EscortMission_HazardousRoute (护送任务危险路线)
•	Context_Mountain_Peak_HighAltitudePass (雪山之巅通道)
•	Context_Jungle_Deep_OvergrownRoads (热带雨林深处蔓生道路)
•	Context_Urban_Flooded_Ruins (被淹没的城市废墟)
•	Context_Bridge_Intercontinental_Damaged (部分完好的跨大陆桥)
•	Context_Settlement_New_Hopeful (充满希望的新建定居点)
•	Context_Volcanic_ActiveZone_Observatory (活火山边缘观测站)
•	Context_Campus_Research_Pharmaceutical (大型制药公司研发园区)
•	Context_Desert_BuriedCity_Ruins (沙暴掩埋的古城遗迹)
•	Context_Coastal_BeachedPlatform_Research (搁浅的深海研究平台)
•	Context_Aerospace_SpaceElevator_RestrictedZone (废弃轨道电梯地面站禁区)
•	Context_Industrial_AutomatedFactory_AIControlled (AI控制的自动化工厂厂区)
•	Context_Forest_GiantTree_CanopyPath (巨型树木树冠层道路)
•	Context_Anomalous_SilentZone (寂静区 - 诡异地带)
•	Context_Cave_CrystalMine (水晶洞穴/矿脉)
•	Context_SciFi_FloatingIsland_Ruins (浮空岛屿残骸区)
•	Context_Anomalous_TimeDistortionZone (时间停滞/错乱区域)
•	Context_BossLair_AlteredReality_Surreal (最终Boss改造的超现实神域)
•	Context_PostApocalyptic_MemorialSite_Hopeful (灾后纪念碑/希望灯塔之地)
•	Context_Bridge_RuinedMegaStructure_OceanCrossing (跨海巨型桥梁废墟)
•	Context_Wasteland_SaltFlats_Expansive (广阔盐碱荒原)
•	Context_Forest_Fungal_Giant (巨型真菌森林)
•	Context_Coastal_ShipGraveyard (海岸线沉船墓地)
•	Context_Aquatic_FloatingCity_Ruined (废弃水上都市)
•	Context_SciFi_FallenSkyCity_ImpactZone_Mountain (天空之城坠落遗迹 - 山区)
•	Context_SciFi_FallenSkyCity_ImpactZone_Plain (天空之城坠落遗迹 - 平原)
•	Context_Polar_Tundra_EternalNightDay (永夜/永昼极地苔原)
•	Context_Desert_Shipwreck_Anomaly_Giant (沙漠巨型幽灵船残骸)
•	Context_Cave_CrystalMine_InfectedZone_Entrance (活体水晶矿洞感染区入口)
•	Context_Wasteland_Path_MobileFortress (移动要塞都市行进路径)
•	Context_Anomalous_SonicWeaponZone (音波禁区/声学武器试验场)
•	Context_SciFi_AlienShipCrashSite_ImpactCrater (异星飞船坠毁撞击坑)
•	Context_Wilderness_MutatedWildlife_Plains (变异动物王国 - 平原)
•	Context_Wilderness_MutatedWildlife_Forest (变异动物王国 - 森林)
•	Context_Wilderness_MutatedWildlife_Swamp (变异动物王国 - 沼泽)
•	Context_Underwater_VolcanicVentField_Active (海底火山带活跃区 - 潜水车)
•	Context_Sky_StormSystem_AirshipEncounter (风暴中空中堡垒遭遇区域)
•	Context_Anomalous_DimensionalRift_OverlappingRealities (镜像维度裂隙重叠区)
•	Context_Surreal_MemoryPalace_ShiftingArchitecture (记忆宫殿扭曲变化建筑群)
•	Context_Desert_BiosphereComplex_Exterior (废弃生物圈外部荒漠控制区)
•	Context_Industrial_MegaJunkyard_RecyclerFaction (回收者巨型垃圾处理厂)
•	Context_Volcanic_DrillPlatformSite (地心钻井平台周边火山区)
•	Context_SciFi_AICore_ExclusionZone_TerraformedAI (AI核心外围改造无人区)
•	Context_SciFi_NaniteGreyGooZone_Flowing (纳米机器人灰蛊区 - 流动吞噬)
•	Context_Aerospace_SpaceElevator_DebrisField_GroundZero (太空电梯基座及轨道残骸坠落区)
•	Context_Remote_HiddenSanctuary_BrainVault_Exterior (缸中之脑庇护所隐蔽外部)
•	Context_Military_BioWeaponTestSite_EnclosedZone (基因编辑生物兵器试验隔离区)
•	Context_SciFi_QuantumComputerRuin_AnomalousZone (量子计算机遗迹异常物理区)
•	Context_SciFi_WeatherControlRuin_ExtremeWeatherZone (天气控制塔废墟及极端天气区)
•	Context_Industrial_CloningFactory_AbandonedComplex (废弃克隆人工厂园区)
•	Context_SciFi_DigitalCemetery_ServerFarmPark (数字公墓服务器陵园)
•	Context_SciFi_SpaceStationCrashSite_ImpactCraterZone (高轨道空间站坠落撞击坑区)
•	Context_Organic_HiveMindNetwork_DominatedZone (蜂巢思维控制中心及网络覆盖区)
•	Context_Transportation_MaglevLine_Abandoned (废弃磁悬浮列车线路及车站)
•	Context_Military_SecretBioLab_DisguisedExterior (黑水公司秘密生化基地伪装外部)
•	Context_SciFi_HolographicCity_ProjectedIllusion (全息投影都市幻城区域)
•	Context_Remote_IsolatedMonastery_Cliffside (与世隔绝的悬崖修道院)
•	Context_Remote_IsolatedMonastery_Island (与世隔绝的孤岛修道院)
•	Context_Aquatic_OilRigCity_VerticalMaze (海上石油钻井平台城市)
•	Context_Organic_WorldTree_MegaStructure (世界树残骸巨型变异植物区)
•	Context_Anomalous_EternalStorm_EyeOfTheStorm_CalmZone (永恒风暴之眼中心平静区)
•	Context_SciFi_LabyrinthCity_ShiftingArchitecture (可移动建筑构成的迷宫之城)
•	Context_Mystical_HiddenTemple_PropheticSite (终末先知圣殿 - 禁地神庙/天文台)
•	Context_SciFi_UtopianDome_ExteriorDefenseZone (伊甸园穹顶外部防御伪装区)
•	Context_Organic_VirusHeartLair_CorruptedZone (病毒之心巢穴高度侵蚀禁区)
•	Context_Aerospace_ArkLaunchSilo_RemoteSite (方舟发射井偏远地区/军事禁区)
•	Context_Remote_OriginLab_ForgottenSite_Forest (零号病人起源实验室 - 深山老林)
•	Context_Remote_OriginLab_ForgottenSite_Island (零号病人起源实验室 - 孤岛)
•	Context_Remote_OriginLab_ForgottenSite_UndergroundBunker (零号病人起源实验室 - 地下掩体)
•	Context_Cosmic_WorldEngine_AccessPoint_Geocentric (世界引擎地心入口区)
•	Context_Cosmic_WorldEngine_AccessPoint_DimensionalGate (世界引擎异次元入口区)
•	Context_Cosmic_MultiverseNexus_ShiftingRealities (多重宇宙交汇点变化景观)
•	Context_Cosmic_AscensionPlace_EnergySanctum (飞升之地能量圣域)
•	Context_Cosmic_VoidOfEntropy_UniverseEnd (寂灭之墟宇宙终结预演地)
•	Context_Cosmic_CreatorWorkshop_StarForgePlatform (造物者星际工坊建造平台)
•	Context_Abstract_ChoiceNexus_EndOfGame (抉择之门抽象时空奇点)
2.0 自然类 (Natural Elements)
此分类涵盖构成自然环境的各种元素，从地形地貌到动植物。
2.1 地形表面类型 (Terrain Surface Type):
•	Surface_Grass_Lush (茂盛草地)
•	Surface_Grass_Dry (干枯草地)
•	Surface_Grass_Sparse (稀疏草地)
•	Surface_Dirt_Loose (松散泥土)
•	Surface_Dirt_Packed (坚实泥土)
•	Surface_Sand_Fine (细沙)
•	Surface_Sand_Coarse (粗沙)
•	Surface_Mud_Wet (湿泥)
•	Surface_Mud_DryCracked (干裂泥地)
•	Surface_Rock_SmoothSheet (平滑岩面)
•	Surface_Rock_JaggedBroken (崎岖碎石面)
•	Surface_Snow_FreshPowder (新雪/粉雪)
•	Surface_Snow_PackedHard (压实雪/硬雪)
•	Surface_Ice_Clear (透明冰面)
•	Surface_Ice_Frosted (霜冰面)
•	Surface_Gravel_Loose (松散砾石)
•	Surface_Leaves_Piled (堆积落叶)
•	Surface_Moss_Ground (地面苔藓)
•	Surface_Ash_Volcanic (火山灰地面)
•	Surface_SaltFlat (盐碱地)
•	Surface_Farmland_Fallow (休耕农田土)
•	Surface_ForestFloor_PineNeedles (森林地面 - 松针)
•	Surface_Quarry_GravelPit (采石场碎石坑底)
•	Surface_Geothermal_SulfurCrust (地热区硫磺地表)
•	Surface_Rock_Wet (湿滑岩面 - 地下洞窟)
•	Surface_Dirt_Barren_Cleared (清理过的裸露土地 - 方舟外围)
•	Surface_Ground_Vitrified_WeaponTest (玻璃化地面 - 武器试验场)
•	Surface_Rock_Volcanic_Basalt (火山岩 - 玄武岩)
•	Surface_Ash_Volcanic_ThickCover (厚火山灰覆盖)
•	Surface_Sand_Deep_CoveringRuins (深层黄沙覆盖遗迹)
•	Surface_SaltFlat_Crystalline (结晶盐碱滩)
•	Surface_Fungal_Carpet_Soft (柔软菌毯地面)
•	Surface_Permafrost_HardFrozen (永久冻土 - 坚硬)
•	Surface_Ice_Sheet_Polar (极地冰盖)
•	Surface_Sand_Patterned_SonicResonance (音波共振沙纹地表)
•	Surface_Seabed_AshCovered_Volcanic (海底火山灰覆盖 - 潜水车)
•	Surface_MicrobialMat_Colorful_Underwater (海底多彩微生物席 - 潜水车)
•	Surface_Ground_DataCableNetwork_AICore (AI核心区数据线缆覆盖地面)
•	Surface_NaniteSwarm_MetallicGrey_Flowing (流动的金属灰纳米机器人集群地表)
•	Surface_Farmland_NewGrowth (新生农田 - 结局后)
•	Surface_Organic_FleshGround_Pulsating (搏动的血肉地面 - 病毒巢穴)
•	Surface_Energy_SolidifiedLight_Ground (固化光能地面 - 飞升之地)
•	Surface_Ground_Barren_ScorchedEarth_Lifeless (死寂焦土 - 寂灭之墟)
•	Surface_Void_Nothingness_EdgeOfReality (虚无空间 - 宇宙边缘)
•	Surface_RealityBlend_DesertSnowForestCity (多重宇宙地貌混合)
2.2 地形特征 (Terrain Feature):
•	Feature_Hill_GentleSlope (缓坡小山)
•	Feature_Hill_SteepSlope (陡坡小山)
•	Feature_Mountain_Slope (山坡)
•	Feature_Mountain_Peak_Snowy (雪山顶)
•	Feature_Cliff_Face_Rock (岩石峭壁)
•	Feature_Cliff_Face_Dirt (土质悬崖)
•	Feature_Valley_Floor (谷底)
•	Feature_Ravine_Narrow (狭窄峡谷)
•	Feature_Riverbed_Dry (干涸河床)
•	Feature_Riverbank_Eroded (受侵蚀的河岸)
•	Feature_Shoreline_SandyBeach (沙滩)
•	Feature_Shoreline_RockyCoast (岩石海岸)
•	Feature_Cave_Entrance_Small (小型洞穴入口)
•	Feature_Cave_Entrance_Large (大型洞穴入口)
•	Feature_Plateau_FlatTop (平顶高原)
•	Feature_Dune_Sand (沙丘)
•	Feature_Arch_NaturalRock (天然石拱)
•	Feature_Sinkhole (天坑/塌陷坑)
•	Feature_Wasteland_Open (开阔荒地)
•	Feature_Farmland_Cultivated_Abandoned (废弃耕地 - 作物枯萎)
•	Feature_Forest_Dense (茂密森林)
•	Feature_Forest_Sparse (稀疏树林)
•	Feature_CanyonWall_Sheer (峡谷峭壁)
•	Feature_Quarry_Pit_Terraced (采石场深坑 - 阶梯状)
•	Feature_Geothermal_Vent_Steam (地热蒸汽口)
•	Feature_Vineyard_Rows_Abandoned (废弃葡萄架行)
•	Feature_Cave_Massive_Underground (巨型地下空腔)
•	Feature_LavaPool_Underground (地下岩浆池)
•	Feature_Trench_AntiTank (反坦克壕沟)
•	Feature_Spillway_Dam (大坝泄洪道)
•	Feature_Trench_Defensive (防御壕沟 - 方舟外围)
•	Feature_Crater_MegaImpact_WeaponTest (巨型弹坑 - 武器试验)
•	Feature_LavaFlow_Advancing (推进中的岩浆流)
•	Feature_Volcano_Crater_ActiveGlow (活火山口红光)
•	Feature_LavaFlow_Distant (远处的岩浆流)
•	Feature_Dune_Sand_Shifting (移动沙丘 - 沙漠古城)
•	Feature_TreeRoots_Giant_ExposedNetwork (巨型树暴露根系网络)
•	Feature_Cave_Entrance_CrystalSurrounded (水晶环绕的洞口)
•	Feature_FloatingIsland_LowAltitude_Ruins (低空浮岛废墟)
•	Feature_Cliff_FloatingIslandEdge (浮岛边缘悬崖)
•	Feature_RockPillar_WindEroded_Salt (风蚀盐柱)
•	Feature_SaltMound_Large (大型盐丘)
•	Feature_FungalCap_Platform_Giant (巨型真菌菌盖平台)
•	Feature_Shoreline_DebrisStrewn_ModernTrash (散布现代垃圾的海岸线)
•	Feature_Quicksand_Trap_Desert (沙漠流沙陷阱)
•	Feature_Oasis_Small_ShipShadow (船影下的小型绿洲)
•	Feature_Cave_Entrance_Crystallizing (晶化中的洞口)
•	Feature_HydrothermalVent_BlackSmoker_Underwater (海底热液喷口 - 黑烟囱)
•	Feature_BeastLair_Giant_Entrance (巨型兽穴入口)
•	Feature_GarbageMountain_Vast (广阔的垃圾山)
•	Feature_SewagePool_Industrial_Large (大型工业污水处理池)
•	Feature_Borehole_MegaDrill_GeothermalVent (超级钻井地热井口)
•	Feature_EnergyNode_AICore_Ground (AI核心区地面能量节点)
•	Feature_NaniteStructure_Mimicry_Terrain (纳米结构拟态地形)
•	Feature_NaniteSwarm_QuicksandTrap (纳米集群流沙陷阱)
•	Feature_NaniteSwarm_SpikeTrap (纳米集群尖刺陷阱)
•	Feature_DebrisField_OrbitalFall_Widespread (轨道坠落物残骸带 - 广阔)
•	Feature_Crater_Impact_SpaceStation (空间站撞击坑)
•	Feature_OrganicNetwork_NeuralFungoid_GroundCover_Massive (巨型神经网络/菌丝覆盖地表)
•	Feature_TreeRoots_WorldTree_MountainSized_Exposed (世界树山脉级根系)
•	Feature_Cave_WorldTreeRootSystem (世界树根系洞穴)
•	Feature_WorldTreeBranch_Platform_Natural (世界树天然树枝平台)
•	Feature_DebrisIsland_StormEye_Accumulated (风暴之眼残骸堆积岛)
•	Feature_SiloOpening_MissileArk_GiantCovered (巨型方舟/导弹发射井口带盖板)
•	Feature_Terrain_NonEuclidean_WorldEnginePerimeter (世界引擎周边非欧几里得地形)
•	Feature_Mountain_CrystallineEnergy_Ascension (飞升之地水晶能量山)
•	Feature_FloatingIsland_LightEnergy_Ascension (飞升之地光能浮岛)
•	Feature_PlanetFragment_Dead_Scattered (死亡星球残骸碎片 - 寂灭之墟)
•	Feature_TimeShard_FrozenMoment_Void (凝固时间碎片 - 寂灭之墟)
•	Feature_Organic_VirusSac_Giant (巨型病毒囊肿)
•	Feature_Organic_VeinNetwork_GroundCover (地面血管网络)
2.3 植被 - 树木 (Vegetation - Trees):
2.3.1 物种/类型 (Species/Type):
* Tree_Pine (松树)
* Tree_Oak (橡树)
* Tree_Birch (桦树)
* Tree_Maple (枫树)
* Tree_Willow (柳树)
* Tree_Palm (棕榈树)
* Tree_Joshua (约书亚树 - 沙漠)
* Tree_Baobab (猴面包树 - 热带)
* Tree_Dead_Leafless (枯树 - 无叶)
* Tree_Burnt_Charred (烧焦的树)
* Tree_Mutant_Twisted (变异扭曲的树)
* Tree_Fungal_Giant (巨型真菌树)
* Tree_Urban_StreetTree_Broken (城市行道树 - 折断)
* Tree_Coniferous_SnowCovered (积雪针叶树 - 冻原)
* Tree_Fungal_Giant_Bioluminescent (发光巨型真菌树)
* Tree_Fungal_Giant_ColorfulCap (多彩菌盖巨型真菌树)
* Tree_MegaFlora_WorldTree_AncientMutant (世界树级远古变异巨树)
**2.3.2 尺寸/年龄 (Size/Age):**
* Size_Seedling (树苗)
* Size_Young (幼树)
* Size_Adult (成年树)
* Size_Mature_LargeCanopy (成熟树 - 冠幅大)
* Size_Giant_Ancient (巨型古树)
* Size_Mega_WorldTree (世界树级超巨型)

**2.3.3 状态 (State):**
* State_Healthy (健康)
* State_Diseased_SpottyLeaves (病态 - 叶片有斑点)
* State_Leafless_Winter (冬季落叶)
* State_Fruiting (结果)
* State_Flowering (开花)
* State_SnowCovered_Light (少量积雪)
* State_SnowCovered_Heavy (大量积雪)
* State_OvergrownWithVines (被藤蔓覆盖)
* State_LightningStruck (被雷击)
* State_Fallen_Uprooted (倒塌 - 被连根拔起/折断)
* State_Wind_Swept (被风吹拂形态 - 沿海/山顶)
* State_Crystallizing_Partial (部分晶化 - 水晶矿洞)
* State_Mutant_AlienEnergyExposure (异星能量暴露导致变异)
* State_Mutant_EarlyVirusExposure (早期病毒暴露导致变异 - 起源实验室)

**2.3.4 密度上下文 (DensityContext - 逻辑标签):**
* Context_Forest_DenseCanopy (茂密森林 - 树冠相连)
* Context_Forest_SparseUndergrowth (稀疏森林 - 林下植被少)
* Context_LoneTree_OpenField (孤树 - 开阔地)
* Context_Grove_SmallCluster (小树丛)
* Context_WindbreakRow (防风林带)
* Context_Roadside_Trees (路边树木)
* Context_Forest_WorldTree_CanopyLayer_Vast (世界树广阔树冠层)
2.4 植被 - 灌木/矮树丛 (Vegetation - Bushes/Shrubs):
2.4.1 物种/类型 (Species/Type):
* Shrub_BerryBush_Edible (浆果灌木 - 可食用)
* Shrub_ThornBush_Defensive (荆棘灌木 - 防御性)
* Shrub_Ornamental_Garden (观赏灌木 - 花园型)
* Shrub_Desert_Tumbleweed (沙漠风滚草)
* Shrub_Dead_Brittle (枯死灌木 - 易碎)
* Shrub_Generic_Green (通用绿色灌木)
* Shrub_Tundra_LowLying (苔原低矮灌木)
* Shrub_SaltTolerant_Sparse (耐盐灌木 - 稀疏)
* Shrub_Tundra_Hardy_Sparse (极地耐寒灌木 - 稀疏)
**2.4.2 尺寸 (Size):**
* Size_Small_Low (小型低矮)
* Size_Medium_Bushy (中型茂密)
* Size_Large_Dense (大型浓密)

**2.4.3 状态 (State):**
* State_Healthy (健康)
* State_Dry_Brown (干枯发黄)
* State_Flowering_Colorful (开花 - 色彩鲜艳)
* State_Overgrown_Wild (过度生长 - 野生)
* State_Snow_Dusted (薄雪覆盖)
* State_Crystallizing_Partial (部分晶化)
* State_Mutant_AlienEnergyExposure (异星能量暴露变异)
2.5 植被 - 地面覆盖物 (Vegetation - Ground Cover):
2.5.1 类型 (Type):
* GroundCover_Grass_Short_Manicured (短草 - 修剪过的)
* GroundCover_Grass_Tall_Wild (高草 - 野生)
* GroundCover_Ferns_ShadyArea (蕨类 - 阴暗区域)
* GroundCover_Flowers_Wild_Mixed (野花 - 混合)
* GroundCover_Ivy_Climbing (爬山虎/常春藤)
* GroundCover_Moss_ShadyRock (苔藓 - 阴湿岩石)
* GroundCover_Fungi_Small_Cluster (小型真菌群)
* GroundCover_Fungi_Glowing_BioLuminescent (发光真菌)
* GroundCover_Cactus_SmallDesert (小型沙漠仙人掌)
* GroundCover_Weeds_Roadside (路边杂草)
* GroundCover_Crop_Corn_Withered (枯萎玉米杆)
* GroundCover_Crop_Sunflower_Dead (枯死向日葵)
* GroundCover_Tundra_MossLichen (苔原苔藓地衣)
* GroundCover_Grass_Manicured_Campus (园区修剪草坪)
* GroundCover_Fungi_MyceliumNetwork_Sticky (粘性菌丝网络)
* GroundCover_Fungi_Poisonous_SmallCluster (小型毒蘑菇群)
* GroundCover_Grass_Manicured_CemeteryPark (陵园公园修剪草坪)
**2.5.2 密度 (Density):**
* Density_SparsePatch (稀疏小块)
* Density_MediumCoverage (中等覆盖)
* Density_DenseCarpet (浓密如地毯)
2.6 岩石/石头 (Rocks/Stones):
2.6.1 类型/尺寸 (Type/Size):
* Rock_Boulder_Large (巨石)
* Rock_Medium_Sized (中型岩石)
* Rock_Small_Scatter (小型散石)
* Rock_Pebble_Cluster (鹅卵石堆)
* Rock_Gravel_Patch (碎石滩)
* RockFormation_Sharp_Angular (尖锐棱角岩层)
* RockFormation_Smooth_Weathered (平滑风化岩层)
* Rock_Slab_Flat (扁平石板)
* Rock_LandslideDebris (塌方落石堆)
* RockFormation_StalactiteStalagmite (钟乳石/石笋)
* Rock_Crystal_Fragments_Scattered (散落水晶碎块)
* Rock_Crystal_Cluster_LargeSharp (大型尖锐水晶簇)
* Rock_Crystal_Growing_OnTerrain (地表生长水晶)
* RockFormation_Cracked_SonicResonance (音波共振裂岩)
* RockFormation_SulphurChimney_Hydrothermal (海底硫磺烟囱)
* Rock_Crystal_StormCharged_Energetic (风暴充能水晶)
**2.6.2 材质/特征 (Material/Feature):**
* Material_Granite (花岗岩)
* Material_Sandstone (砂岩)
* Material_Volcanic_Obsidian (火山岩/黑曜石)
* Feature_MossCovered (覆盖苔藓)
* Feature_LichenCovered (覆盖地衣)
* Feature_MineralVein (矿脉裸露)
* Feature_SnowDusted (薄雪覆盖的岩石)
2.7 水体 (Water Bodies):
2.7.1 类型 (Type):
* Water_River_FastFlowing (急流河)
* Water_River_SlowMeandering (缓流蜿蜒河)
* Water_Stream_Shallow (浅溪)
* Water_Lake_ClearBlue (清澈湖泊 - 蓝色)
* Water_Lake_MurkyGreen (浑浊湖泊 - 绿色)
* Water_Pond_SmallStill (小池塘 - 静水)
* Water_Swamp_Stagnant (沼泽积水 - 停滞)
* Water_Ocean_CoastalWave (海洋 - 近岸浪花)
* Water_Puddle_Rain (雨水坑)
* Water_Puddle_Muddy (泥水坑)
* Water_Geyser_HotSpring (间歇泉/温泉)
* Water_Reservoir_Dam (水坝水库)
* Water_River_Underground (地下暗河)
* Water_Lake_Polluted_DebrisFilled (污染漂浮物湖泊 - 水上都市)
* Water_River_LiquidEnergy_Shimmering (液态能量河 - 飞升之地)
**2.7.2 特征 (Feature):**
* Feature_Waterfall_SmallCascade (小型瀑布)
* Feature_Waterfall_LargePlunge (大型瀑布)
* Feature_Rapids_Whitewater (急流险滩)
* Feature_StillWater_Reflective (静水面 - 反光)
* Feature_IceSheet_FrozenSurface (冰盖 - 冻结水面)
* Feature_AlgaeBloom_Surface (藻类大量繁殖覆盖水面)
* Feature_River_ShallowFord (河流浅滩可通行)
* Feature_Coastline_TidePools (海岸潮汐池)
* Feature_Geothermal_SteamMist (地热蒸汽薄雾)
3.0 建筑类 (Building Elements)
此分类涵盖构成建筑物主体和重要组成部分的模块。
3.1 建筑类型 (Building Type - 宏观分类):
•	BuildingType_Residential_House_SingleFamily (独栋住宅)
•	BuildingType_Residential_Apartment_LowRise (低层公寓楼 2-4层)
•	BuildingType_Residential_Apartment_MidRise (中层公寓楼 5-8层)
•	BuildingType_Commercial_Store_Small (小型商店/零售店)
•	BuildingType_Commercial_Store_Large_Supermarket (大型商店/超市)
•	BuildingType_Commercial_Office_Small (小型办公楼)
•	BuildingType_Commercial_Office_MidRise (中层办公楼)
•	BuildingType_Industrial_Warehouse_Small (小型仓库)
•	BuildingType_Industrial_Warehouse_Large (大型仓库)
•	BuildingType_Industrial_Factory_Light (轻工业工厂)
•	BuildingType_Industrial_Factory_Heavy (重工业工厂)
•	BuildingType_Military_Barracks (兵营)
•	BuildingType_Military_Bunker_AboveGround (地上碉堡)
•	BuildingType_Military_Bunker_UndergroundEntrance (地下掩体入口)
•	BuildingType_Public_Hospital_Small (小型医院)
•	BuildingType_Public_School_Elementary (小学)
•	BuildingType_Public_PoliceStation_Small (小型警察局)
•	BuildingType_Public_FireStation (消防站)
•	BuildingType_Religious_Church_SmallTown (小镇教堂)
•	BuildingType_Agricultural_Barn (谷仓/农棚)
•	BuildingType_Agricultural_Silo (筒仓)
•	BuildingType_Makeshift_Shack_Wood (临时木棚)
•	BuildingType_Makeshift_Shack_Metal (临时铁皮棚)
•	BuildingType_Transportation_GasStation (加油站主体建筑)
•	BuildingType_Transportation_BusTerminal_Small (小型公交总站建筑)
•	BuildingType_Utility_PowerSubstationBuilding (变电站建筑)
•	BuildingType_Utility_Garage_RepairShop (修车厂/车间)
•	BuildingType_Utility_ParkingGarage_Structure (多层停车场建筑)
•	BuildingType_Public_Mall_Shopping (大型购物中心)
•	BuildingType_Residential_Farmhouse (农舍)
•	BuildingType_Utility_RoadMaintenanceDepot_Abandoned (废弃道班房)
•	BuildingType_Commercial_Motel_Desert (沙漠汽车旅馆)
•	BuildingType_Utility_ForestRangerStation (护林员小屋)
•	BuildingType_Utility_TunnelControlBuilding (隧道管理中心)
•	BuildingType_Utility_Lighthouse (灯塔)
•	BuildingType_Utility_Observatory_Weather (气象观测站)
•	BuildingType_Research_PolarOutpost (极地科考前哨站)
•	BuildingType_Public_VisitorCenter_NationalPark (国家公园游客中心)
•	BuildingType_Industrial_QuarryOffice (采石场办公室)
•	BuildingType_Industrial_QuarryExplosivesShed (采石场炸药库)
•	BuildingType_Residential_FishingHut (渔民小屋)
•	BuildingType_Commercial_FishMarketStall (海鲜市场棚屋)
•	BuildingType_Military_CommunicationRelay (军事通讯基站楼)
•	BuildingType_Utility_DamControlBuilding (水坝控制楼)
•	BuildingType_Utility_PowerPlant_Dam (水力发电厂房)
•	BuildingType_Commercial_WineryBuilding (葡萄酒庄园主建筑)
•	BuildingType_Industrial_WineryProduction (葡萄酒酿酒厂)
•	BuildingType_Aerospace_LaunchControlCenter (航天发射控制中心)
•	BuildingType_Aerospace_VehicleAssemblyBuilding (火箭总装大楼)
•	BuildingType_Aerospace_FuelDepot (燃料储存罐/设施)
•	BuildingType_Public_Stadium_Sports (体育场)
•	BuildingType_Public_ExhibitionHall (会展中心)
•	BuildingType_Public_Museum_History (历史博物馆)
•	BuildingType_Public_Museum_Art (艺术馆)
•	BuildingType_Vehicle_ShipSuperstructure (船只上层建筑)
•	BuildingType_Military_Hangar_Aircraft (飞机库)
•	BuildingType_Military_AmmoDepot (弹药库)
•	BuildingType_Military_CommandBunker (指挥벙커)
•	BuildingType_Military_Watchtower_Automated (自动武器瞭望塔)
•	BuildingType_Military_Pillbox_Concrete (混凝土碉堡)
•	BuildingType_Industrial_PowerPlant_Nuclear (核电站)
•	BuildingType_Industrial_PowerPlant_FossilFuel (火电站)
•	BuildingType_Utility_CoolingTower_Large (大型冷却塔)
•	BuildingType_Utility_ReactorBuilding (反应堆建筑)
•	BuildingType_Utility_TurbineHall (涡轮机房)
•	BuildingType_Utility_Dam_Hydroelectric_Massive (巨型水电站大坝)
•	BuildingType_City_Fortified_Ark (方舟堡垒都市)
•	BuildingType_Military_Watchtower_CityWall (城墙瞭望塔 - 方舟)
•	BuildingType_Residential_Apartment_FuturisticBlock (未来感公寓楼 - 方舟)
•	BuildingType_Commercial_Market_BlackMarket_Hidden (黑市商店 - 方舟)
•	BuildingType_Government_AdminBuilding_Large (大型行政大楼 - 方舟)
•	BuildingType_Military_MissileSilo_Large (大型导弹发射井)
•	BuildingType_Research_Lab_Underground_Fortified (加固地下实验室 - 武器试验场)
•	BuildingType_Military_ControlCenter_WeaponTest (武器试验控制中心)
•	BuildingType_Military_ObservationPost_Reinforced (加固观察哨 - 武器试验场)
•	BuildingType_Makeshift_CommunityShelter_New (新建社区避难所)
•	BuildingType_Research_Observatory_Volcano (火山观测站)
•	BuildingType_Research_Lab_ModernComplex (现代化复合实验楼)
•	BuildingType_Commercial_Office_ModernCampus (现代化园区办公楼)
•	BuildingType_Historic_CityWall_SandCovered (沙埋古城墙)
•	BuildingType_Historic_Tower_SandCovered (沙埋古塔楼)
•	BuildingType_Industrial_Platform_Research_Offshore_Beached (搁浅的离岸研究平台)
•	BuildingType_Aerospace_SpaceElevator_BaseStation_Massive (巨型轨道电梯地面站)
•	BuildingType_Industrial_Factory_Automated_Clean (AI控制的自动化洁净工厂)
•	BuildingType_Organic_TreeHollowDwelling (树洞居所)
•	BuildingType_SciFi_FloatingStructure_Ruined (科技浮空建筑废墟)
•	BuildingType_Structure_Monument_PostApoc (灾后纪念碑)
•	BuildingType_Structure_Tower_HopeBeacon (希望灯塔)
•	BuildingType_Makeshift_Settlement_PostApoc_Small (小型灾后定居点)
•	BuildingType_Military_BridgeControlTower_Damaged (受损桥梁控制塔)
•	BuildingType_Structure_BridgePier_Massive_Damaged (受损巨型桥墩)
•	BuildingType_Industrial_SaltMineShack_Abandoned (废弃盐矿工棚)
•	BuildingType_Research_Outpost_SaltFlat_Buried (半埋的盐碱地科研前哨)
•	BuildingType_Research_BioLabTent_Abandoned_FungalForest (废弃生物研究帐篷群 - 真菌森林)
•	BuildingType_Utility_Lighthouse_Ruined_Coastal (废弃海岸灯塔)
•	BuildingType_Makeshift_FloatingCity_PlatformNetwork (废弃船只平台拼接的水上都市)
•	BuildingType_Makeshift_Shack_Floating_MultiStorey (水上多层临时建筑)
•	BuildingType_SciFi_SkyScraper_FallenSection (坠落天空之城摩天楼残骸)
•	BuildingType_Research_PolarStation_Abandoned_Icebound (冰封废弃极地科考站)
•	BuildingType_Military_SubmarineBase_IceCaveEntrance (冰封潜艇基地入口建筑)
•	BuildingType_Industrial_MinersCamp_CrystalMine_Abandoned (废弃水晶矿工营地)
•	BuildingType_MobileFortress_MegaStructure_IndustrialSteampunk (移动巨型要塞都市-工业蒸汽朋克)
•	BuildingType_Research_AcousticLab_Weaponized_Abandoned (废弃声学武器实验室)
•	BuildingType_Shelter_Soundproofed_Abandoned (废弃隔音避难所)
•	BuildingType_Utility_ZooControlCenter_Abandoned (废弃动物园控制中心)
•	BuildingType_SciFi_UnderwaterCity_Dome_Ruined (废弃海底穹顶城市)
•	BuildingType_MobileFortress_Airship_Stormrider (风暴追逐者空中堡垒)
•	BuildingType_SciFi_DimensionalStabilizer_Ruined (废弃维度稳定器控制室建筑)
•	BuildingType_Historic_Library_Distorted (扭曲的图书馆 - 记忆宫殿)
•	BuildingType_Historic_Manor_Labyrinthine_Distorted (迷宫般扭曲的庄园 - 记忆宫殿)
•	BuildingType_Research_BiosphereDome_Giant_Abandoned (废弃巨型生物圈2号穹顶)
•	BuildingType_Research_Lab_BiosphereSupport (生物圈配套科研楼)
•	BuildingType_Utility_PowerPlant_BiosphereSupport (生物圈配套能源站)
•	BuildingType_Residential_Dormitory_BiosphereSupport (生物圈配套生活区宿舍)
•	BuildingType_Industrial_MetalShredder_Giant (巨型金属破碎机厂房)
•	BuildingType_Industrial_Incinerator_TallChimney (高烟囱焚烧炉厂房)
•	BuildingType_Makeshift_CommandPlatform_JunkTower (垃圾山顶回收王指挥平台)
•	BuildingType_Industrial_SuperDrillPlatform_Geothermal (地热超级钻井平台)
•	BuildingType_Residential_HabitatModule_Platform (平台居住模块)
•	BuildingType_SciFi_AICore_Underground_Spherical (地下球形AI核心建筑)
•	BuildingType_SciFi_AICore_MountainEmbedded_Polyhedral (山体内多面体AI核心建筑)
•	BuildingType_Research_NanotechLab_Ruined_Overrun (被侵蚀的纳米技术实验室废墟)
•	BuildingType_Aerospace_SpaceElevator_BaseStation_Ruined (废弃太空电梯基座)
•	BuildingType_Facility_ConcealedEntrance_Underground (隐蔽的地下设施入口 - 缸中之脑)
•	BuildingType_Research_QuantumComputerFacility_ExteriorShell_Damaged (量子计算机设施外部受损壳体)
•	BuildingType_SciFi_WeatherControlTower_Mega_Ruined (巨型天气控制塔废墟)
•	BuildingType_Industrial_Factory_Standardized_Cloning (标准化克隆人工厂厂房)
•	BuildingType_Structure_MemorialPark_WithHiddenTech (隐藏科技的纪念公园建筑)
•	BuildingType_Facility_UndergroundServerFarm_EntranceVisible (可见入口的地下服务器农场)
•	BuildingType_Organic_HiveMind_CentralBrainStructure_Exterior (蜂巢思维中央母脑外部结构)
•	BuildingType_Transportation_MaglevStation_Grand_Abandoned (废弃大型磁悬浮车站)
•	BuildingType_Utility_PowerStation_MaglevLine_Abandoned (废弃磁悬浮供电站)
•	BuildingType_Industrial_MaintenanceDepot_Maglev_Abandoned (废弃磁悬浮维修基地)
•	BuildingType_Religious_Monastery_Ancient_Remote_Eerie (与世隔绝的古老诡异修道院)
•	BuildingType_Industrial_OilPlatformCity_InterconnectedRigs (多个石油钻井平台连接的城市)
•	BuildingType_Residential_ContainerHousing_OilRig (石油钻井平台集装箱生活区)
•	BuildingType_Organic_WorldTree_CanopyVillage_Primitive (世界树树冠原始村落)
•	BuildingType_Research_WeatherStation_StormEye_Abandoned (永恒风暴之眼废弃气象站)
•	BuildingType_SciFi_EnergyCollector_StormPowered_Damaged (损坏的风暴能源收集装置建筑)
•	BuildingType_SciFi_ControlTower_ShiftingCity_Central (迷宫之城中央控制塔)
•	BuildingType_Religious_Temple_Ancient_Forbidden (禁地古老神庙 - 先知圣殿)
•	BuildingType_Research_Observatory_Ancient_Astrological (古老星象观测台 - 先知圣殿)
•	BuildingType_SciFi_EcoDome_Utopia_Massive (巨型乌托邦生态穹顶)
•	BuildingType_Aerospace_FuelingStation_Rocket (火箭燃料加注厂)
•	BuildingType_Aerospace_AstronautTrainingFacility_Abandoned (废弃宇航员训练基地)
•	BuildingType_Research_Lab_Abandoned_Overgrown_VirusOrigin (废弃蔓生病毒起源实验室)
•	BuildingType_Structure_GeoElevator_WorldEngineAccess (世界引擎地心电梯井口建筑)
•	BuildingType_Structure_ObserverOutpost_Multiverse (多重宇宙观察者哨站)
•	BuildingType_Structure_Temple_EnergyConduit_Ascension (飞升之地能量汇聚神殿)
•	BuildingType_Structure_LastCivilizationMonument_Void (最后文明墓碑 - 寂灭之墟)
•	BuildingType_SciFi_MegaStructure_OrbitalForge_Creator (造物者星际工坊轨道平台)
•	BuildingType_Urban_Generic_Holographic (全息投影城市通用建筑)
3.2 建筑风格 (Architectural Style - Low Poly下简化):
•	Style_Blocky_Simple (方块简约风)
•	Style_Angular_Modernish (棱角现代感)
•	Style_Rustic_WoodHeavy (乡村木质感)
•	Style_Industrial_MetalPanel (工业金属板)
•	Style_Fortified_ConcreteBlock (防御工事混凝土块)
•	Style_Prefab_Modular (预制模块化)
•	Style_Historic_BrickFacade (历史感砖砌外墙)
•	Style_Utility_Concrete_Functional (功能性混凝土 - 加油站/服务区)
•	Style_Rural_StoneWood (乡村石木结构 - 道班房)
•	Style_Research_InsulatedPrefab (科考站预制板 - 极地)
•	Style_Coastal_WeatherBeaten (沿海风雨侵蚀风格)
•	Style_Winery_Estate (酒庄庄园风格)
•	Style_Aerospace_Functional (航天设施功能风格)
•	Style_Historic_Grand (宏伟古典风 - 博物馆)
•	Style_Modern_Grand (宏伟现代风 - 博物馆)
•	Style_Military_ReinforcedConcrete (军事加固混凝土)
•	Style_PowerPlant_IndustrialUtilitarian (能源厂工业实用风)
•	Style_Fortification_MegaCityWall (巨型城市防御墙风格)
•	Style_Futuristic_CleanUtopian (未来感洁净乌托邦风 - 方舟城)
•	Style_SciFi_WeaponTestFacility (科幻武器试验设施风)
•	Style_Fortified_HeatResistant (加固耐高温 - 火山观测站)
•	Style_Modern_Campus_Research (现代化研究园区风)
•	Style_Ancient_Stone_DesertRuin (古代石头沙漠废墟风 - 沙埋古城)
•	Style_Industrial_OffshorePlatform (离岸平台工业风)
•	Style_Aerospace_MegaStructure_Base (巨型航天结构基座风)
•	Style_Industrial_Automated_Sterile (自动化无菌工业风 - AI工厂)
•	Style_Organic_LivingWood (活体木质有机风 - 巨树之城)
•	Style_SciFi_FloatingArchitecture_Ruined (科幻浮空建筑废墟风)
•	Style_Makeshift_Hopeful_NewSettlement (临时希望感新建定居点风)
•	Style_Industrial_SaltMine_Basic (盐矿基础工业风)
•	Style_Organic_FungalForest_Alien (异形真菌森林有机风)
•	Style_Makeshift_FloatingJunk (漂浮垃圾拼凑风 - 水上都市)
•	Style_SciFi_FallenSkyCity_DamagedTech (坠落天空之城破损科技风)
•	Style_Polar_Research_Fortified (极地科考加固风)
•	Style_Steampunk_Industrial_MobileFortress (蒸汽朋克工业移动要塞风)
•	Style_SciFi_AcousticWeaponLab_Abandoned (废弃声学武器实验室科幻风)
•	Style_AlienTech_CrashSite_NonEuclidean (异星科技坠毁地非欧几里得风)
•	Style_SciFi_UnderwaterDome_Ruined (废弃海底穹顶科幻风)
•	Style_Airship_StormChaser_Modified (改装追风者飞空艇风格)
•	Style_Surreal_Shifting_MemoryPalace (超现实变化记忆宫殿风)
•	Style_SciFi_Biosphere_GlassSteelDome (科幻生物圈玻璃钢穹顶风)
•	Style_Industrial_Junkyard_MegaScale (巨型垃圾场工业风)
•	Style_Industrial_MegaDrill_Geothermal (地热超级钻井平台工业风)
•	Style_SciFi_AICore_Geometric_Massive (AI核心巨型几何体风格)
•	Style_Camouflaged_AsRuin (伪装成废墟风格)
•	Style_SciFi_QuantumFacility_Anomalous (量子设施异常风格)
•	Style_SciFi_WeatherTower_MegaRuined (巨型天气塔废墟风格)
•	Style_Industrial_CloningFacility_Sterile (克隆工厂无菌工业风)
•	Style_SciFi_DigitalMemorial_Park (数字纪念公园科幻风)
•	Style_Organic_HiveMind_BioMechanical (蜂巢思维生物机械风)
•	Style_Transportation_MaglevStation_FuturisticAbandoned (未来感废弃磁悬浮车站风)
•	Style_Camouflaged_AsFactory (伪装成工厂风格)
•	Style_Camouflaged_AsFarm (伪装成农场风格)
•	Style_Historic_Stone_Monastic_Ominous (古老石头修道院不祥风)
•	Style_Organic_MegaFlora_WorldTree (巨型植物世界树有机风)
•	Style_SciFi_ShiftingCity_Modular (模块化移动城市科幻风)
•	Style_Ancient_Mystical_Symbolic_Alien (古老神秘异形符号风 - 先知圣殿)
•	Style_Camouflaged_AsMountain_ExteriorDome (伪装成山脉 - 穹顶外部)
•	Style_Camouflaged_AsForest_ExteriorDome (伪装成森林 - 穹顶外部)
3.3 尺寸类别 (Size Category - 逻辑分类):
•	SizeCat_Micro (微型 - 如岗亭)
•	SizeCat_Small (小型 - 如独立小屋、小型店铺)
•	SizeCat_Medium (中型 - 如标准住宅、小型办公楼)
•	SizeCat_Large (大型 - 如仓库、超市、学校)
•	SizeCat_ExtraLarge_Landmark (超大型/地标性 - 如工厂综合体、大型医院)
•	SizeCat_Colossal (巨像级 - 世界引擎、造物者工坊)
3.4 层数 (Storeys):
•	Storeys_Single (单层)
•	Storeys_Two (双层)
•	Storeys_ThreeToFive (三至五层)
•	Storeys_SixToTen (六至十层)
•	Storeys_HighRise_ElevenPlus (高层 - 十一层及以上)
•	Storeys_Basement_Exists (有地下室)
•	Storeys_Underground_MultiLevel (多层地下 - 停车场)
•	Storeys_Structure_VeryTall_TowerLike (极高塔状结构 - 天气塔/灯塔)
3.5 主要建材 (PrimaryConstructionMaterial - 视觉主导):
•	Material_Brick_Red (红砖)
•	Material_Brick_Yellow (黄砖)
•	Material_Concrete_Bare (裸露混凝土)
•	Material_Concrete_Painted (涂漆混凝土)
•	Material_Wood_Plank_Horizontal (水平木板)
•	Material_Wood_Log (原木)
•	Material_Metal_CorrugatedSheet (波纹金属板)
•	Material_Metal_SmoothPanel (光滑金属板)
•	Material_Stone_RoughCut (粗切石块)
•	Material_Stucco_Painted (粉刷灰泥)
•	Material_Glass_FacadeDominant (玻璃幕墙主导)
•	Material_Steel_Frame (钢结构 - 高架桥，工厂)
•	Material_Tarpaulin_Makeshift (防水布 - 临时营地)
•	Material_Steel_Reinforced_Military (军事强化钢材)
•	Material_Composite_SciFi_Futuristic (科幻复合材料 - 方舟城)
•	Material_Obsidian_VolcanicRock (黑曜石/火山岩 - 火山观测站)
•	Material_FungalChitin_Hardened (硬化真菌甲壳 - 真菌森林建筑)
•	Material_ScrapMetal_Patchwork (废金属拼接 - 水上/垃圾场都市)
•	Material_AlienAlloy_Unknown (未知外星合金 - 飞船残骸)
•	Material_Glass_SteelFrame_Dome (玻璃钢架穹顶 - 生物圈)
•	Material_NaniteComposite_SelfRepairing (纳米复合材料 - AI核心)
•	Material_SoundAbsorbingPanel_Exterior (外部吸音板 - 寂静区建筑)
•	Material_LivingWood_WorldTree (活体世界树木质)
•	Material_Glass_TransparentShield_Dome (透明护盾穹顶材料 - 伊甸园)
•	Material_Organic_FleshBone_Virus (病毒血肉骨骼 - 病毒巢穴)
•	Material_EnergyField_Solidified (固化能量场 - 飞升之地/世界引擎)
3.6 状况/状态 (Condition/State):
•	State_Intact_Newish (完好如新)
•	State_Intact_Weathered (完好但风化)
•	State_Damage_Minor_Cracks (轻微损坏 - 裂纹)
•	State_Damage_Moderate_Holes (中等损坏 - 破洞)
•	State_Damage_Heavy_PartialCollapse (严重损坏 - 部分坍塌)
•	State_Ruined_Skeleton (废墟 - 仅剩骨架)
•	State_Burnt_Exterior (外部烧毁)
•	State_Burnt_Interior (内部烧毁)
•	State_Overgrown_Vines_Facade (外墙被藤蔓覆盖)
•	State_BoardedUp_WindowsDoors (门窗被木板封死)
•	State_Looted_Empty (被搜刮一空)
•	State_Makeshift_Fortified (临时加固)
•	State_DustCovered_Thick (覆盖厚厚灰尘)
•	State_Bloodstained (血迹斑斑)
•	State_WaterDamaged_FloodedBasement (水淹地下室/底层)
•	State_SandCovered_Partial (部分被沙掩埋 - 沙漠建筑)
•	State_FrostCovered (覆盖霜雪 - 极地建筑)
•	State_Corroded_Chemical (化学腐蚀 - 工业区)
•	State_ImpactDamage_Vehicle (车辆撞击损坏)
•	State_Damage_Structural_Uplift (地壳抬升结构损坏 - 研究平台)
•	State_Damage_Sinking_Tilting_FloatingStructure (倾斜下沉 - 漂浮建筑)
•	State_Damage_NaniteDecomposition_Partial (纳米机器人部分分解)
•	State_Damage_Heavy_StructuralCollapse_Mega (巨型结构严重坍塌 - 太空电梯)
•	State_Overgrown_MutatedFlora_OriginLab (变异植物覆盖 - 起源实验室)
•	State_Building_Mobile_Modular (模块化可移动建筑)
3.7 屋顶类型 (Roof Type):
•	Roof_Flat_Concrete (平顶 - 混凝土)
•	Roof_Flat_Tar (平顶 - 油毛毡)
•	Roof_Pitched_Gable_AsphaltShingle (坡顶 - 人字形 - 沥青瓦)
•	Roof_Pitched_Gable_MetalSheet (坡顶 - 人字形 - 金属板)
•	Roof_Pitched_Hip_Tile (坡顶 - 四坡形 - 瓦片)
•	Roof_Shed_SingleSlope (单坡屋顶)
•	Roof_Domed_Small (小型穹顶)
•	Roof_Damaged_Holes (损坏屋顶 - 有破洞)
•	Roof_Collapsed_Partial (部分坍塌屋顶)
•	Roof_NoRoof_ExposedInterior (无屋顶 - 内部暴露)
•	Roof_Garden_Overgrown (屋顶花园 - 杂草丛生)
•	Roof_Parking_Asphalt (屋顶停车场 - 沥青)
•	Roof_Domed_Large_Stadium (大型穹顶 - 体育馆)
•	Roof_GlassSteel_Dome_Biosphere (玻璃钢架穹顶 - 生物圈)
•	Roof_TransparentShield_EcoDome (透明护盾穹顶 - 伊甸园)
3.8 外立面模块特征 (Facade Module Features - 用于模块资产本身):
3.8.1 墙体段 (Wall Sections):
* Wall_Solid_NoOpenings (实心墙 - 无开口)
* Wall_WindowOpening_Single (单窗洞墙)
* Wall_WindowOpening_Multiple (多窗洞墙)
* Wall_DoorOpening_Single (单门洞墙)
* Wall_DoorOpening_Double (双门洞墙)
* Wall_LargeOpening_Garage (大型开口 - 车库门)
* Wall_Damaged_Hole_Small (损坏墙 - 小破洞)
* Wall_Damaged_Hole_Large (损坏墙 - 大破洞)
* Wall_Crumbling_Edge (边缘 crumbling 墙)
* Wall_Corner_Exterior (外角墙)
* Wall_Corner_Interior (内角墙)
* Wall_CorrugatedMetal_Panel (波纹金属板墙面)
* Wall_ConcreteBlock_Industrial (工业混凝土砌块墙)
* Wall_Military_ReinforcedConcrete_FiringSlit (军事加固混凝土墙带射击孔)
* Wall_Containment_HighSecurity_BioLab (高级生物实验室安全围墙)
3.8.2 窗户 (Windows - 作为可嵌入模块):**
* Window_Type_SingleHung (单悬窗)
* Window_Type_DoubleHung (双悬窗)
* Window_Type_Casement (平开窗)
* Window_Type_Picture (景观窗/固定窗)
* Window_Size_Small_Square (小方窗)
* Window_Size_Medium_Rectangular (中型矩形窗)
* Window_Size_Large_Panoramic (大型全景窗)
* Window_State_Intact_Clear (完好 - 透明玻璃)
* Window_State_Intact_Dirty (完好 - 肮脏玻璃)
* Window_State_Cracked (玻璃龟裂)
* Window_State_Broken_Shattered (玻璃破碎)
* Window_State_Boarded_Wood (木板封堵)
* Window_State_Shuttered_Metal (金属百叶窗关闭)
* Window_Frame_Wood (木窗框)
* Window_Frame_Metal (金属窗框)
* Window_Type_Shopfront_Large (大型商店橱窗)
* Window_Type_Bay (凸窗)
* Window_Observatory_Slit_Reinforced (加固观察狭缝窗 - 火山观测站)
* Window_Porthole_Ship_Platform (船/平台舷窗)

**3.8.3 门 (Doors - 作为可嵌入模块):**
* Door_Type_Single_Wood (单扇木门)
* Door_Type_Single_Metal (单扇金属门)
* Door_Type_Double_Wood_GlassPanel (双扇木门 - 带玻璃板)
* Door_Type_Double_Metal_Industrial (双扇金属工业门)
* Door_Type_Garage_RollUp (卷帘车库门)
* Door_Type_Sliding_Patio (滑动庭院门)
* Door_State_Closed_Locked (关闭锁住)
* Door_State_Closed_Unlocked (关闭未锁)
* Door_State_Open_Slightly (微开)
* Door_State_Open_Wide (敞开)
* Door_State_Broken_OffHinge (损坏 - 脱离合页)
* Door_State_Missing (门丢失)
* Door_State_Boarded (木板封堵)
* Door_Type_Security_Reinforced (强化安全门 - 地下车库设备间)
* Door_Type_Glass_StoreEntrance (玻璃商店入口门 - 便利店)
* Door_Type_LoadingBay_Large (大型装卸区大门 - 仓库)
* Door_Type_BlastDoor_Heavy_Military (重型防爆门 - 军事벙커)
* Door_Type_Gate_MegaCity_Massive (巨型城市闸门 - 方舟)
* Door_Type_Airlock_SciFi_Facility (科幻设施气闸门)
* Door_Type_Gate_EcoDome_HighSecurity (生态穹顶高安全闸门)

3.8.4 阳台/延伸物 (Balconies/Extensions):**
* Balcony_Small_Concrete (小型混凝土阳台)
* Balcony_Large_MetalRailing (大型金属栏杆阳台)
* Awning_ShopFront_Canvas (店面帆布遮阳篷)
* FireEscape_Metal_External (外部金属消防梯)
* Porch_Wood_Small (小型木制门廊)
* LoadingDock_Concrete_Warehouse (仓库混凝土装卸平台)
* Canopy_GasStation_Large (加油站大型遮雨棚)
* Catwalk_Industrial_Metal (工业金属猫道/走道 - 工厂/仓库)
* ObservationDeck_Lighthouse_Top (灯塔顶部观察台)
* Gantry_RocketLaunchPad_ServiceArm (火箭发射架服务臂/龙门架)
3.9 内部可访问性 (Interior Access - 逻辑标签):
•	Access_Enterable_Full (完全可进入)
•	Access_Enterable_Partial_GroundFloorOnly (部分可进入 - 仅底层)
•	Access_NonEnterable_FacadeOnly (不可进入 - 仅外壳)
•	Access_Enterable_ViaDamage (通过损坏处可进入)
3.10 功能性 (Functionality - 逻辑标签, POI相关):
•	Function_POI_Resource_Fuel (兴趣点 - 燃料资源)
•	Function_POI_Resource_FoodWater (兴趣点 - 食水资源)
•	Function_POI_Resource_Medical (兴趣点 - 医疗资源)
•	Function_POI_Resource_WeaponAmmo (兴趣点 - 武器弹药)
•	Function_POI_Shelter_SafeZone (兴趣点 - 安全区/庇护所)
•	Function_POI_QuestGiver_NPCLocation (兴趣点 - 任务发布点/NPC位置)
•	Function_POI_CraftingStation (兴趣点 - 工作台)
•	Function_POI_Lore_ComputerTerminal (兴趣点 - 传说/信息终端)
•	Function_POI_Resource_VehicleParts (兴趣点 - 车辆零件)
•	Function_POI_Navigation_Map (兴趣点 - 地图/导航信息)
•	Function_POI_Vendor_Trader (兴趣点 - 商人/交易点)
•	Function_POI_Resource_VariedGood (兴趣点 - 各类物资 - 仓库)
•	Function_POI_Resource_ScientificEquipment (兴趣点 - 科研设备 - 科考站)
•	Function_POI_Resource_SeedBank_GeneticMaterial (兴趣点 - 种子/基因样本)
•	Function_POI_Lore_MissionCriticalInfo (兴趣点 - 任务关键信息)
•	Function_POI_Resource_EnergyCell_PowerSource (兴趣点 - 能源核心/电池)
•	Function_POI_Resource_Antidote_CureComponent (兴趣点 - 解药/关键成分)
•	Function_POI_Resource_AlienTech_Artifact (兴趣点 - 外星科技/神器)
•	Function_POI_Lore_Prophecy_Oracle (兴趣点 - 预言/神谕)
4.0 装饰类 (Decorative Elements / Props - 室外特定)
此分类主要指独立于建筑主体结构，用于丰富场景、提供掩护或叙事的室外道具。
4.1 街道家具 (Street Furniture):
•	StreetFurniture_Bench_Wood_Park (公园木长凳)
•	StreetFurniture_Bench_Metal_BusStop (公交站金属长凳)
•	StreetFurniture_TrashCan_Metal_Upright (金属垃圾桶 - 直立)
•	StreetFurniture_TrashCan_Plastic_Overturned (塑料垃圾桶 - 翻倒)
•	StreetFurniture_Mailbox_Residential (住宅邮箱)
•	StreetFurniture_PhoneBooth_Damaged_GlassBroken (电话亭 - 玻璃破碎)
•	StreetFurniture_BusShelter_MetalGlass_Damaged (公交候车亭 - 金属玻璃 - 损坏)
•	StreetFurniture_Planter_Concrete_Empty (混凝土花坛 - 空)
•	StreetFurniture_Planter_Wood_OvergrownWeeds (木花坛 - 长满杂草)
•	StreetFurniture_BicycleRack_Empty (自行车停放架 - 空)
•	StreetFurniture_Newsstand_Overturned (报刊亭 - 翻倒)
•	StreetFurniture_PicnicTable_Wood_Park (公园木制野餐桌)
•	StreetFurniture_Playground_Slide_Damaged (儿童滑梯 - 损坏)
•	StreetFurniture_Playground_SwingSet_Broken (秋千架 - 损坏)
•	StreetFurniture_Sculpture_Plaza_Damaged (广场雕塑 - 损坏)
4.2 标牌/广告牌 (Signage/Billboards):
•	Signage_StreetName_PoleMounted (路牌 - 杆式)
•	Signage_Traffic_StopSign_Bent (交通标志 - 停止牌 - 弯曲)
•	Signage_Traffic_SpeedLimit_Faded (交通标志 - 限速牌 - 褪色)
•	Signage_Billboard_Large_BlankFrame (大型广告牌 - 空框架)
•	Signage_Billboard_Medium_TatteredAd (中型广告牌 - 破损广告)
•	Signage_Shop_Neon_BrokenLetters (商店霓虹灯招牌 - 字母损坏)
•	Signage_Shop_Hanging_Swinging (商店悬挂招牌 - 摇晃)
•	Signage_Warning_Radiation_Rusty (警告牌 - 辐射 - 生锈)
•	Signage_Directional_Makeshift (临时指示牌)
•	Signage_TrafficLight_Deformed (交通信号灯 - 变形)
•	Signage_GasStation_PriceSign_Damaged (加油站价格招牌 - 损坏)
•	Signage_Highway_Directional_Rusty (高速公路指示牌 - 生锈/倾倒)
•	Signage_ParkingGarage_LevelIndicator (停车场楼层指示 - 模糊)
•	Signage_Building_Directory_Faded (建筑导览牌 - 褪色)
•	Signage_BusRoute_Map_Damaged (公交线路图 - 损坏)
•	Signage_Park_InformationBoard_Weathered (公园信息板 - 风化)
•	Signage_Mine_Warning_Danger (矿区警告牌 - 危险)
•	Signage_Factory_NamePlate_Rusty (工厂名牌 - 生锈)
•	Signage_Lighthouse_Name (灯塔名称标牌)
•	Signage_Quarry_AreaSign (采石场区域指示牌)
•	Signage_Winery_NameSign (葡萄酒庄园名称标牌)
•	Signage_Aerospace_AreaDesignation (航天发射中心区域标示牌)
•	Signage_MilitaryBase_Warning_RestrictedArea (军事基地警告牌 - 禁区)
•	Signage_PowerPlant_Warning_HighVoltage (发电厂警告牌 - 高压危险)
•	Signage_CityGate_ArkIdentification (方舟城门身份识别标牌)
•	Signage_WeaponTestSite_Warning_DangerZone (武器试验场警告牌 - 危险区域)
•	Signage_VolcanoObservatory_EvacuationRoute (火山观测站疏散路线图)
•	Signage_Pharmaceutical_CompanyLogo_Damaged (制药公司Logo标牌 - 损坏)
•	Signage_Biohazard_Warning_ContainmentZone (生物危害警告牌 - 隔离区)
•	Signage_AICore_Warning_RestrictedAccess (AI核心禁区警告)
•	Signage_NaniteZone_Warning_Corrosive (纳米机器人区腐蚀警告)
•	Signage_CloningFacility_Identification_Faded (克隆工厂识别标牌 - 褪色)
•	Signage_Monastery_AncientScript_Warning (修道院古文警告牌匾)
•	Signage_Temple_PropheticSymbol_Weathered (先知圣殿预言符号牌 - 风化)
•	Signage_EcoDome_WelcomeToEden_PristineOrDamaged (伊甸园欢迎牌 - 完好或损坏)
•	Signage_ArkLaunchFacility_ProjectName_Faded (方舟发射设施项目名牌 - 褪色)
•	Signage_OriginLab_TopSecret_Warning (起源实验室最高机密警告)
4.3 围栏/障碍 (Fencing/Barriers):
•	Fence_Wood_Picket_BrokenSections (木栅栏 - 尖桩 - 多处断裂)
•	Fence_Chainlink_Bent_Hole (铁丝网围栏 - 弯曲 - 有破洞)
•	Fence_BarbedWire_Tangled (带刺铁丝网 - 缠绕)
•	Wall_Brick_Low_Crumbling (矮砖墙 - 摇摇欲坠)
•	Barricade_PoliceTape_Torn (警戒线 - 撕裂)
•	Barricade_Sandbags_Leaking (沙袋工事 - 漏沙)
•	Barricade_ConcreteBarrier_Jersey_Cracked (混凝土防撞墩 - 泽西护栏 - 裂纹)
•	Barricade_Makeshift_FurnitureDebris (临时路障 - 家具杂物)
•	Bollard_Metal_Bent (金属系缆桩/路桩 - 弯曲)
•	Roadblock_Military_TankTrap_Rusty (军事反坦克障碍 - 生锈)
•	Roadblock_Container_Single (单个集装箱路障)
•	Gate_Industrial_ChainLink_Damaged (工业铁丝网大门 - 损坏)
•	GuardShack_Checkpoint_Abandoned (废弃检查站岗亭)
•	TireWall_Makeshift (轮胎墙 - 临时防御)
•	Wall_Military_HighConcrete_Damaged (高混凝土军事围墙 - 破损)
•	Fence_Chainlink_Electrified (通电铁丝网)
•	Fence_BarbedWire_Concertina (蛇腹形带刺铁丝网)
•	Wall_Fortification_MegaCity_ConcreteSteel (巨型城市防御墙 - 混凝土钢铁)
•	Fence_Security_High (高安全围栏 - 地下种子银行)
•	Wall_Fortification_Stealth_MultiLayered (多层隐形防御墙 - 缸中之脑)
•	Wall_Containment_HighSecurity_BioLab (高级生物实验室安全围墙)
•	Wall_Security_BioLab_Perimeter (生化基地外围安保墙)
4.4 公共设施/设备 (Utilities/Equipment):
•	Utility_PowerPole_Wood_LeaningWires (木制电线杆 - 电线倾斜)
•	Utility_PowerPole_Concrete_DamagedBase (混凝土电线杆 - 底部损坏)
•	Utility_TransformerBox_Ground_Sparking (地面变压器箱 - 冒火花)
•	Utility_FireHydrant_Leaking (消防栓 - 漏水)
•	Utility_FireHydrant_Broken (消防栓 - 撞断)
•	Utility_ManholeCover_Open_Steaming (窨井盖 - 打开 - 冒蒸汽)
•	Utility_ManholeCover_Missing (窨井盖 - 丢失)
•	Utility_SatelliteDish_Rooftop_Misaligned (屋顶卫星天线 - 错位)
•	Utility_AC_Unit_WallMounted_Noisy (壁挂空调外机 - 噪音)
•	Utility_Vent_Industrial_Rusty (工业通风口 - 生锈)
•	Utility_Payphone_Vandalized (公用电话 - 被破坏)
•	Utility_SecurityCamera_BrokenLens (监控摄像头 - 镜头破碎)
•	Utility_GasPump_Damaged_Leaking (加油泵 - 损坏泄漏)
•	Utility_AirPump_GasStation (加油站充气泵)
•	Utility_Antenna_LargeRadio_Bent (大型无线电天线 - 弯曲)
•	Utility_SurveillanceTower_Abandoned (废弃瞭望塔 - 营地/工业区)
•	Utility_Floodlight_Industrial_Broken (工业泛光灯 - 损坏)
•	Utility_WindTurbine_Small_Damaged (小型风力发电机 - 损坏 - 极地)
•	Utility_Generator_Portable_Broken (便携式发电机 - 损坏)
•	Utility_Pipeline_AboveGround_Rusty (地上管道 - 生锈 - 工业区/地热)
•	Utility_SolarPanel_Cracked (太阳能板 - 破裂)
•	Utility_WaterTower_Rusty (锈蚀水塔 - 农场/小镇)
•	Utility_ConveyorBelt_Industrial_Broken (工业传送带 - 断裂)
•	Utility_LaunchPad_Gantry_Rusty (火箭发射架 - 锈蚀)
•	Utility_Vent_Tunnel_Broken (隧道通风口 - 破损)
•	Utility_AutomatedTurret_Base (自动炮塔基座)
•	Utility_PowerLineTower_HighVoltage_Large (大型高压输电塔)
•	Utility_HeavyWeaponEmplacement (重型武器平台)
•	Utility_EnergyShieldGenerator_Large (大型能量护盾发生器)
•	Utility_Crane_HeavyDuty_Platform (重型起重机 - 研究平台)
•	Utility_Smokestack_Industrial_Active (运作中的工业烟囱 - 移动要塞)
•	Utility_HeavyCannon_FortressMounted (移动要塞搭载的重炮)
•	Utility_SteamVent_Defensive_FortressMounted (移动要塞防御蒸汽喷口)
•	Utility_SonicEmitter_Weaponized_DamagedGiant (巨型受损声波武器发射器)
•	Utility_AlienDefenseTurret_Automated_Active (异星自动防御炮塔 - 运作中)
•	Utility_Pipeline_Underwater_Damaged (受损海底管道)
•	Utility_WeaponEmplacement_AirshipMounted (飞空艇武器平台)
•	Utility_EnergyShieldGenerator_AirshipMounted (飞空艇能量护盾发生器)
•	Utility_HeavyMachinery_DrillingRig (重型钻井机械)
•	Utility_Pipeline_PlatformSurface (平台表面管道)
•	Utility_HeatSink_AICore_GiantExternal (AI核心巨型外部散热片)
•	Utility_EnergyConduit_AICore_External (AI核心外部能量导管)
•	Utility_AutomatedTurret_AICoreDefense (AI核心防御炮塔)
•	Utility_AutomatedTurret_SpaceElevatorDebris_Active (太空电梯残骸激活的自动炮塔)
•	Utility_SensorArray_Advanced_Hidden (高级隐蔽传感器阵列)
•	Utility_AIDefenseSystem_QuantumComputer (量子计算机AI防御系统)
•	Utility_ElectromagneticCoil_Giant_WeatherTowerTop (天气塔顶巨型电磁线圈)
•	Utility_EnergyEmitter_WeatherTowerTop (天气塔顶能量发射器)
•	Utility_StorageTank_Liquid_Giant_CloningFacility (克隆工厂巨型液体储存罐)
•	Utility_SignalTower_Maglev_Damaged (受损磁悬浮信号塔)
•	Utility_SecurityCamera_Advanced_BioLab (生化基地高级监控摄像头)
•	Utility_AutomatedSentryGun_BioLab (生化基地自动哨戒炮)
•	Utility_HologramProjector_CityScale_HiddenInRuin (隐藏在废墟中的城市规模全息投影仪)
•	Utility_EnergyShield_DomeDefense (穹顶防御能量护盾 - 伊甸园)
4.5 车辆残骸 (Vehicle Wrecks - 作为静态道具):
•	VehicleWreck_Car_Sedan_DoorsOpen (轿车残骸 - 车门打开)
•	VehicleWreck_Car_Sedan_Burnt (轿车残骸 - 烧毁)
•	VehicleWreck_Car_Pickup_CargoSpilled (皮卡残骸 - 货物散落)
•	VehicleWreck_Van_SideImpact (厢式货车残骸 - 侧面撞击)
•	VehicleWreck_Bus_WindowsShattered (公交车残骸 - 窗户破碎)
•	VehicleWreck_Bus_Burnt (公交车残骸 - 烧毁)
•	VehicleWreck_Truck_TrailerDetached (卡车残骸 - 拖车分离)
•	VehicleWreck_Truck_Container_Damaged (集装箱卡车残骸 - 箱体损坏)
•	VehicleWreck_Motorcycle_Fallen_PartsMissing (摩托车残骸 - 倒地 - 零件丢失)
•	VehicleWreck_Bicycle_Rusty_Tangled (自行车残骸 - 生锈 - 缠绕)
•	VehicleWreck_Military_Jeep_Burnt (军用吉普车残骸 - 烧毁)
•	VehicleWreck_Military_APC_Destroyed (军用装甲运兵车残骸 - 被毁)
•	VehicleWreck_Construction_Excavator_Abandoned (工程挖掘机 - 废弃)
•	VehicleWreck_Farm_Tractor_Rusty (农用拖拉机残骸 - 生锈)
•	VehicleWreck_TrainCar_Derelict_Tracks (废弃火车车厢 - 铁轨上)
•	VehicleWreck_Forklift_Abandoned_Warehouse (废弃叉车 - 仓库)
•	VehicleWreck_CamperVan_Abandoned (露营车残骸 - 废弃)
•	VehicleWreck_Snowmobile_Damaged (雪地摩托残骸 - 损坏)
•	VehicleWreck_Boat_Small_Beached (小型船只残骸 - 搁浅)
•	VehicleWreck_Yacht_Damaged_Docked (游艇残骸 - 损坏停靠)
•	VehicleWreck_Aircraft_FighterJet_Deck (战斗机残骸 - 航母甲板)
•	VehicleWreck_Train_Metro_Abandoned (废弃地铁列车)
•	VehicleWreck_Construction_Generic_Abandoned (废弃施工设备)
•	VehicleWreck_Mining_HaulTruck_Giant (巨型矿用卡车残骸)
•	VehicleWreck_Construction_Excavator_Giant (巨型挖掘机残骸)
•	VehicleWreck_Military_Tank_Destroyed (被毁坦克残骸)
•	VehicleWreck_Military_Truck_Destroyed (被毁军用卡车残骸)
•	VehicleWreck_Boat_Ancient_Fragment (古老船只残骸碎片)
•	VehicleWreck_Ship_Cargo_Grounded_Multiple (多艘搁浅货轮残骸)
•	VehicleWreck_Ship_Warship_Grounded_Multiple (多艘搁浅军舰残骸)
•	VehicleWreck_Ship_IntegratedIntoPlatform (融入平台的船只残骸 - 水上都市)
•	VehicleWreck_Ship_Cargo_Deserted_Massive (巨型废弃货轮 - 沙漠)
•	VehicleWreck_Ship_Warship_Deserted_Massive (巨型废弃战舰 - 沙漠)
•	VehicleWreck_AlienShip_Fragment_Large (大型异星飞船残骸碎片)
•	VehicleWreck_Truck_Haul_JunkyardCustom (垃圾场改装运输车残骸)
•	VehicleWreck_Construction_Modified_Junkyard (垃圾场改装工程机械残骸)
•	VehicleWreck_SpaceStation_MainModule_Fragment (空间站主模块残骸)
•	VehicleWreck_SpaceStation_SolarPanelArray_Fragment (空间站太阳能板阵列残骸)
•	VehicleWreck_SpaceStation_HabitatModule_Fragment (空间站居住舱模块残骸)
•	VehicleWreck_MaglevTrain_Derelict (废弃磁悬浮列车)
4.6 一般碎片/杂物 (General Debris/Clutter):
•	Debris_Barrel_Metal_Rusty_Dent (金属桶 - 生锈 - 凹陷)
•	Debris_Barrel_Plastic_LeakingFluid (塑料桶 - 泄漏液体)
•	Debris_Crate_Wood_Broken_Empty (木箱 - 破碎 - 空)
•	Debris_Pallet_Wood_Stacked (木托盘 - 堆叠)
•	Debris_Tire_Single (单个轮胎)
•	Debris_Tire_Pile_Small (小型轮胎堆)
•	Debris_RubblePile_ConcreteBrick_Small (混凝土砖块瓦砾堆 - 小型)
•	Debris_RubblePile_MetalScrap_Large (金属废料堆 - 大型)
•	Debris_Pipes_Scattered_Rusty (散落的管道 - 生锈)
•	Debris_Newspaper_Scattered_Windblown (散落的报纸 - 被风吹)
•	Debris_Clothes_Discarded_Dirty (丢弃的衣物 - 肮脏)
•	Debris_ShoppingBag_Plastic_Torn (塑料购物袋 - 撕裂)
•	Debris_CardboardBox_Flattened (压扁的纸板箱)
•	Debris_GlassShards_Scattered (碎玻璃片 - 散落)
•	Debris_CarParts_Scattered (汽车零件 - 散落 - 修车厂)
•	Debris_OilDrum_Empty_Rusty (空油桶 - 生锈)
•	Debris_GasCan_Empty (空汽油罐)
•	Debris_FoodCans_Empty_Scattered (空食品罐头 - 散落)
•	Debris_Luggage_Scattered_Damaged (行李箱 - 散落损坏)
•	Debris_Sandbags_Torn_Empty (沙袋 - 破损漏沙)
•	Debris_ShippingContainer_Damaged_Empty (集装箱 - 损坏空置)
•	Debris_Tools_Scattered_Rusty (工具 - 散落生锈 - 修车厂)
•	Debris_FishingNet_Tangled_Beach (渔网 - 缠绕 - 沙滩)
•	Debris_Shells_Beach (贝壳 - 沙滩)
•	Debris_IndustrialWaste_Piles (工业废料堆)
•	Debris_ChemicalBarrel_Leaking (化学品桶 - 泄漏)
•	Debris_MiningEquipment_Broken (损坏的采矿设备零件)
•	Debris_WineBarrels_Broken (破碎的葡萄酒桶)
•	Debris_RocketParts_Scattered (火箭部件残骸 - 散落)
•	Debris_Building_Rubble_Large (大型建筑瓦砾堆)
•	Debris_SciFi_MetalStructure_GiantFragment (科幻金属结构巨型碎片 - 天空之城)
•	Debris_Building_SciFi_PanelFragment (科幻建筑面板碎片)
•	Debris_Pipes_SciFi_Twisted (扭曲的科幻管道)
•	Debris_LabEquipment_Outdoor_Scattered (室外散落的实验设备残骸)
4.7 叙事性道具 (Narrative Props - 室外):
•	Narrative_Luggage_Abandoned_Open (被遗弃的行李箱 - 打开)
•	Narrative_Toy_Childs_TeddyBear_Dirty (儿童玩具 - 泰迪熊 - 肮脏)
•	Narrative_MakeshiftGraveMarker_WoodCross (临时墓碑 - 木十字架)
•	Narrative_Campfire_Extinguished_Ashes (熄灭的篝火 - 灰烬)
•	Narrative_Tent_Collapsed_TornFabric (倒塌的帐篷 - 织物撕裂)
•	Narrative_Radio_Broken_Static (坏掉的收音机 - 静电噪音)
•	Narrative_BarricadeSign_SurvivorMessage (路障标语 - 幸存者留言)
•	Narrative_MissingPersonPoster_Faded (寻人启事 - 褪色)
•	Narrative_ShoppingCarts_Abandoned_Overturned (废弃购物车 - 翻倒)
•	Narrative_SleepingBag_Dirty_Abandoned (肮脏的废弃睡袋)
•	Narrative_Backpack_Abandoned (废弃背包)
•	Narrative_Roadmap_Discarded (丢弃的路线图)
•	Narrative_Journal_Weathered_Survivor (风化的幸存者日记)
•	Narrative_ScientificNotes_FieldResearch (实地科研笔记 - 极地)
•	Narrative_DistressBeacon_Damaged (损坏的求救信标)
•	Narrative_Weapon_UnexplodedOrdnance_Experimental (未爆炸的实验武器)
4.8 状态 (State - 通用，可叠加):
•	State_Prop_Intact (道具完好)
•	State_Prop_Rusty (道具生锈)
•	State_Prop_Broken_Minor (道具轻微损坏)
•	State_Prop_Broken_Major (道具严重损坏)
•	State_Prop_Overturned (道具翻倒)
•	State_Prop_Burnt (道具烧焦)
•	State_Prop_CoveredInDust (道具布满灰尘)
•	State_Prop_CoveredInSnow (道具积雪)
•	State_Prop_OvergrownWithWeeds (道具长满杂草)
•	State_Prop_LeakingFluid (道具泄漏液体)
•	State_Prop_Bloodstained (道具沾染血迹)
•	State_Prop_FrozenSolid (道具完全冻结 - 极地)
•	State_Prop_SandCovered (道具被沙覆盖 - 沙漠)
•	State_Prop_PartiallySubmerged (道具部分淹没/沉没)
•	State_Prop_SandCovered_Heavy (道具被重沙覆盖)
•	State_Prop_Rusty_Extreme (道具极端锈蚀)
•	State_Prop_TimeFrozen_DynamicPose (道具时间凝固 - 动态姿势)
4.x 新增独立道具类别 (New Independent Prop Categories):
•	Prop_Ship_Lifeboat_Damaged (破损的船用救生艇)
•	Prop_Cable_Giant_Broken_Hanging (断裂悬挂的巨型缆绳 - 太空电梯)
•	Prop_Robot_Patrol_AutomatedFactory (自动化工厂巡逻机器人)
•	Prop_Robot_Maintenance_AutomatedFactory (自动化工厂维护机器人)
•	Prop_Pontoon_FloatingPlatform_Base (浮动平台用浮筒)
•	Prop_SciFi_AntiGravEngine_Damaged_Erratic (损坏的不稳定反重力引擎)
•	Prop_Bones_Megafauna_Scattered (巨型动物骸骨散落)
•	Prop_Robot_Combat_AICoreDefense_Various (多种型号AI核心防御战斗机器人)
•	Prop_Cable_SpaceElevator_GiantSegment_Fallen (坠落的太空电梯巨型缆绳段)
•	Prop_OrbitalModule_SpaceElevator_CrashFragment (坠毁的太空电梯轨道舱碎片)
•	Prop_MemorialStone_DigitalInscription_Subtle (带有不易察觉数字铭文的纪念石)
•	Prop_Cocoon_HiveMind_AssimilationPod (蜂巢思维同化荚/茧)
•	Prop_Portal_Dimensional_WorldEngineAccess (通往世界引擎的维度传送门)
•	Prop_Portal_Multiverse_RandomFixed (通往多重宇宙的随机/固定传送门)
•	Prop_Gate_AbstractChoice_GlowingPortal (抉择之门的抽象发光传送门)
•	Prop_Gear_Giant_Rotating_Visible (可见的旋转巨型齿轮 - 移动要塞)
5.0 贴花类 (Decal Elements)
此分类用于在已生成的表面上添加细节和叙事痕迹。
5.1 损坏类型 (Damage Type):
•	Decal_Crack_Wall_Fine (墙壁细裂纹)
•	Decal_Crack_Wall_LargeBranching (墙壁大分叉裂纹)
•	Decal_Crack_Ground_Asphalt (地面沥青裂纹)
•	Decal_Crack_Glass_Spiderweb (玻璃蛛网状裂纹)
•	Decal_Pothole_RoadSurface (路面坑洞贴花)
•	Decal_BulletHoles_Single_SmallCaliber (单弹孔 - 小口径)
•	Decal_BulletHoles_Spray_Automatic (多弹孔 - 自动武器扫射)
•	Decal_BulletImpact_RicochetMark (跳弹痕迹)
•	Decal_ExplosionScorch_Small_Circular (小型爆炸焦痕 - 圆形)
•	Decal_ExplosionScorch_Large_Irregular (大型爆炸焦痕 - 不规则)
•	Decal_Scratch_Metal_Deep (金属深刮痕)
•	Decal_Dent_VehicleBody_Medium (车身中等凹痕)
•	Decal_ChippedPaint_Edge (边缘掉漆)
•	Decal_BlastCrater_Small_Ground (小型爆炸坑贴花 - 地面)
•	Decal_BlastCrater_Large_Ground (大型爆炸坑贴花 - 地面)
•	Decal_ClawMarks_Giant_GroundBuilding (巨型爪痕 - 地面/建筑)
5.2 液体/污渍类型 (Liquid/Stain Type):
•	Decal_Blood_Splat_Fresh (血迹喷溅 - 新鲜)
•	Decal_Blood_Trail_Smear (血迹拖痕/涂抹)
•	Decal_Blood_Pool_Dried_Dark (干涸血泊 - 暗色)
•	Decal_Oil_Stain_DarkGreasy (油渍 - 深色油腻)
•	Decal_Water_Puddle_Reflective (水坑贴花 - 反光)
•	Decal_Water_Streak_Wall_RunDown (墙壁水痕 - 流淌状)
•	Decal_Rust_Streak_MetalSurface (金属表面锈迹流痕)
•	Decal_Grime_Patch_DarkDirty (污垢块 - 深色肮脏)
•	Decal_Moss_Growth_Wall_Shady (墙壁苔藓生长 - 阴暗处)
•	Decal_Mold_Patch_DampArea (霉斑 - 潮湿区域)
•	Decal_Soot_SmokeStain_Ceiling (烟灰污渍 - 天花板)
•	Decal_Corrosion_ChemicalSpill (化学品泄漏腐蚀痕迹)
•	Decal_FuelSpill_Iridescent (燃料泄漏彩虹光泽)
•	Decal_LiquidSpill_Viscous_CultureMedium (粘稠培养基泄漏痕迹)
5.3 涂鸦/标记 (Graffiti/Markings):
5.3.1 风格 (Style):
* Graffiti_Style_Tag_QuickSpray (标签式 - 快速喷涂)
* Graffiti_Style_Stencil_Symbolic (模板式 - 符号)
* Graffiti_Style_Text_BlockLetters (文字式 - 方块字)
* Graffiti_Style_Text_HandwrittenCrude (文字式 - 粗糙手写)
* Graffiti_Style_Drawing_Childish (涂鸦画 - 幼稚)
* Graffiti_Style_Arrow_SimpleDirection (箭头 - 简单方向)
•	* Graffiti_Style_Symbol_AncientMystic_Carved (古老神秘符号 - 雕刻)
5.3.2 内容 (Content - 逻辑标签):**
•	* Graffiti_Content_Message_SafeZone (信息 - 安全区)
•	* Graffiti_Content_Message_DangerAhead (信息 - 前方危险)
•	* Graffiti_Content_Message_LootHere_Maybe (信息 - 此处可能有物资)
•	* Graffiti_Content_Message_FactionSymbol_Unknown (派系符号 - 未知)
•	* Graffiti_Content_Message_SurvivorNote_Desperate (幸存者留言 - 绝望)
•	* Graffiti_Content_Message_Warning_Infected (警告 - 已感染)
•	* Graffiti_Content_Message_Directional_Exit (方向指示 - 出口)
•	* Graffiti_Content_Message_Political_AntiAuthority (政治性 - 反抗标语)
•	* Graffiti_Content_Message_Territory_FactionX (X派系地盘标记)
•	
5.3.3 可辨识度 (Legibility):**
•	* Legibility_Clear_Readable (清晰可读)
•	* Legibility_Faded_Partial (部分褪色)
•	* Legibility_Obscured_Damaged (被遮挡/损坏)
•	* Legibility_Illegible_Smudged (难以辨认/涂抹)
5.4 环境效果 (Environmental Effects):
•	Decal_DustLayer_Surface_Thick (厚灰尘层覆盖表面)
•	Decal_Ash_Pile_Small_Ground (小堆灰烬贴花 - 地面)
•	Decal_LeafLitter_Patch_Autumn (落叶堆积贴花 - 秋季)
•	Decal_Mud_Splatters_LowWall (泥点飞溅 - 低矮墙面)
•	Decal_TyreTracks_DirtRoad (轮胎痕迹 - 土路)
•	Decal_TyreTracks_Asphalt_Skidmark (轮胎痕迹 - 沥青路面急刹车痕)
•	Decal_Footprints_Muddy_SingleSet (脚印 - 泥泞 - 单组)
•	Decal_SnowDrift_Small_AgainstObject (小型雪堆 - 靠物体)
•	Decal_SandDrift_Small_AgainstObject (小型沙堆 - 靠物体 - 沙漠)
•	Decal_BurnMark_Ground_FlamingObject (地面燃烧物痕迹)
•	EnvironmentalEffect_RadiationLeak_Area (辐射泄漏区域效果)
•	EnvironmentalEffect_SteamVent_HighTemp (高温蒸汽喷发效果)
•	EnvironmentalEffect_HeavySmoke_Obscuring (遮蔽视线的浓烟)
•	EnvironmentalEffect_SulfurFumes_Air (空气中硫磺烟雾)
•	EnvironmentalEffect_HighWinds_DebrisFall (强风及坠物效果 - 大陆桥)
•	EnvironmentalEffect_SaltDevil_Wind (盐尘旋风)
•	EnvironmentalEffect_SporeCloud_Dense_Obscuring (浓密遮蔽孢子云)
•	EnvironmentalEffect_GravityDistortion_Localized (局部重力扭曲)
•	EnvironmentalEffect_Aurora_Sky_Eerie (诡异极光天空效果)
•	EnvironmentalEffect_AvalancheRisk_Area (雪崩危险区域效果)
•	EnvironmentalEffect_Sandstorm_PersistentWinds (持续性沙尘暴风)
•	EnvironmentalEffect_CrystalDust_Airborne_Hazardous (有害水晶粉尘空气效果)
•	EnvironmentalEffect_SonicPulse_Damaging_Area (区域性破坏音波脉冲)
•	EnvironmentalEffect_AntiGravityField_Localized (局部反重力场)
•	EnvironmentalEffect_SpatialDistortion_Localized (局部空间扭曲)
•	EnvironmentalEffect_VolcanicAshCloud_Underwater_Obscuring (水下火山灰云遮蔽)
•	EnvironmentalEffect_HurricaneForceWinds_Airborne (空中飓风级狂风)
•	EnvironmentalEffect_TorrentialRain_Airborne (空中暴雨)
•	EnvironmentalEffect_LightningStrikes_Frequent_Airborne (空中频繁闪电)
•	EnvironmentalEffect_ColorAberration_Visual (视觉色彩失真)
•	EnvironmentalEffect_ObjectPhasing_Visual (物体时隐时现效果)
•	EnvironmentalEffect_ToxicFumes_Air_Junkyard (垃圾场有毒烟雾)
•	EnvironmentalEffect_NaniteDustCloud_Corrosive_VehiclePath (车辆路径腐蚀性纳米尘云)
•	EnvironmentalEffect_DebrisFall_Orbital_Random (随机轨道坠物)
•	EnvironmentalEffect_ProbabilityFlux_Localized (局部概率异常)
•	EnvironmentalEffect_GravityFluctuation_Localized (局部引力波动)
•	EnvironmentalEffect_MicroStorm_RainHail_Frequent (频繁局部暴雨冰雹)
•	EnvironmentalEffect_Tornado_Local_Frequent (频繁局部龙卷风)
•	EnvironmentalEffect_ElectromagneticStorm_Violent (剧烈电磁风暴)
•	EnvironmentalEffect_ChemicalFumes_Air_CloningFacility (克隆工厂化学药剂气味)
•	EnvironmentalEffect_AlienMicrobeContamination_Area (外星微生物污染区域)
•	EnvironmentalEffect_CosmicRadiationHotspot_Area (宇宙辐射热点区域)
•	EnvironmentalEffect_PsychicResonance_HiveMind_Area (蜂巢思维精神共鸣区域)
•	EnvironmentalEffect_StormWall_Perpetual_Surrounding (环绕的永久风暴墙)
•	EnvironmentalEffect_Lightning_OverheadCloudLayer_Constant (头顶云层持续闪电)
•	EnvironmentalEffect_FetidAir_VirusSpores_Dense (浓烈恶臭病毒孢子空气)
•	EnvironmentalEffect_ExoticEnergyField_WorldEnginePerimeter (世界引擎周边奇异能量场)
•	EnvironmentalEffect_PureEnergyAura_SoothingLight (纯净能量柔光氛围 - 飞升之地)
•	Decal_TerritoryMarking_Beast (野兽地盘标记)
5.5 目标表面 (Target Surface - 逻辑标签，用于放置规则):
•	TargetSurface_Wall_Concrete (墙面 - 混凝土)
•	TargetSurface_Wall_Brick (墙面 - 砖块)
•	TargetSurface_Road_Asphalt (路面 - 沥青)
•	TargetSurface_Metal_Sheet (金属板表面)
•	TargetSurface_Wood_Plank (木板表面)
•	TargetSurface_Glass_Window (玻璃窗表面)
•	TargetSurface_Ground_Dirt (地面 - 泥土)
•	TargetSurface_Vehicle_Body (车辆表面)
•	TargetSurface_Organic_Flesh (有机血肉表面 - 病毒巢穴)
•	TargetSurface_AlienAlloy_ShipHull (异星合金船体表面)
5.6 形状/形态 (Shape/Form):
•	Shape_Linear_Trail (线性拖痕)
•	Shape_Circular_Pool (圆形水坑/血泊)
•	Shape_Irregular_Splat (不规则喷溅)
•	Shape_Rectangular_Patch (矩形块状)
•	Shape_SprayPattern_Conical (锥形喷射图案)
•	Decal_AlienGlyph_Glowing_ShipHull (异星飞船船体发光符文贴花)
5.x 新增贴花/环境效果类别
•	EnvironmentalEffect_CorpsePile_Heavy (大量尸骸堆积效果 - 围城)
这份详细的标签列表应该能为您在Unity中构建灵活且富有表现力的程序化Low Poly末日场景提供坚实的基础。在实际应用中，您可以根据具体的主题和性能预算，选择性地实现和应用这些标签。
________________________________________

